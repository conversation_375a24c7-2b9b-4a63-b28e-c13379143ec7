<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实施路线图</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'SourceHanSansCN';
        src: url('https://cdn.jsdelivr.net/npm/source-han-sans-cn@1.001/SubsetOTF/CN/SourceHanSansCN-Bold.otf') format('opentype');
        font-weight: bold;
      }
      @font-face {
        font-family: 'MicrosoftYaHei';
        src: url('https://cdn.jsdelivr.net/npm/microsoft-yahei@1.0.0/Microsoft-YaHei.ttf') format('truetype');
      }
      .slide-container {
        width: 100%;
        max-width: 1280px;
        min-height: 720px;
        margin: 0 auto;
        background: #F4F6F7;
        color: #1A5276;
        font-family: 'MicrosoftYaHei', sans-serif;
        display: flex;
      }
      .left-section {
        width: 30%;
        background: #1A5276;
        color: #FFFFFF;
        padding: 2rem 1.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .right-section {
        width: 70%;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .title {
        font-family: 'SourceHanSansCN', sans-serif;
        font-size: clamp(1.5rem, 4vw, 2.25rem);
        font-weight: bold;
        margin-bottom: 1rem;
      }
      .subtitle {
        font-size: clamp(1rem, 2vw, 1.25rem);
        line-height: 1.5;
      }
      .chart-section {
        margin-bottom: 1.5rem;
      }
      .chart-title {
        font-size: 1.125rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .chart-title i {
        margin-right: 0.5rem;
      }
      .phase-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }
      .phase-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: flex;
        min-height: 80px;
      }
      .phase-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #2E86C1;
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 1rem;
        flex-shrink: 0;
      }
      .phase-icon i {
        font-size: 1.25rem;
      }
      .phase-content {
        flex-grow: 1;
      }
      .phase-title {
        font-size: 1rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.5rem;
      }
      .phase-description {
        font-size: 0.875rem;
        line-height: 1.4;
      }
      .milestone {
        display: flex;
        align-items: center;
        margin-top: 0.25rem;
      }
      .milestone i {
        color: #2E86C1;
        margin-right: 0.5rem;
        font-size: 0.75rem;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">实施路线图</div>
        <div class="subtitle">
          平台建设采用分阶段实施策略，总体周期约4个月（13周），分为三个主要阶段逐步推进，确保项目能够快速见效并持续优化。
        </div>
      </div>
      <div class="right-section">
        <div class="chart-section">
          <div class="chart-title">
            <i class="fas fa-tasks"></i> 项目进度规划
          </div>
          <div style="height: 180px;">
            <canvas id="roadmapChart"></canvas>
          </div>
        </div>
        
        <div class="phase-container">
          <div class="phase-item">
            <div class="phase-icon">
              <i class="fas fa-rocket"></i>
            </div>
            <div class="phase-content">
              <div class="phase-title">第一阶段：公众查询服务快速上线（2周内）</div>
              <div class="phase-description">
                快速解决公众查询服务缺失的紧迫问题，建立用户对平台的初步信心和认知。
                <div class="milestone"><i class="fas fa-flag"></i>里程碑：网站查询服务和微信公众号集成上线</div>
              </div>
            </div>
          </div>
          
          <div class="phase-item">
            <div class="phase-icon">
              <i class="fas fa-database"></i>
            </div>
            <div class="phase-content">
              <div class="phase-title">第二阶段：系统管理和数据管理体系建设（3-10周）</div>
              <div class="phase-description">
                建立完整的系统管理体系和地质灾害数据的信息化管理，为预警发布功能提供支撑。
                <div class="milestone"><i class="fas fa-flag"></i>里程碑：用户管理、权限控制和数据管理功能完成</div>
              </div>
            </div>
          </div>
          
          <div class="phase-item">
            <div class="phase-icon">
              <i class="fas fa-bullhorn"></i>
            </div>
            <div class="phase-content">
              <div class="phase-title">第三阶段：预警发布机制完善（10-13周）</div>
              <div class="phase-description">
                建立多渠道预警发布机制，完善系统管理功能，实现平台的完整功能体系。
                <div class="milestone"><i class="fas fa-flag"></i>里程碑：预警发布覆盖率达到95%以上，系统全面运行</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="navigation">
        <a href="advantages.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="value_analysis.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>
    
    <script>
      // 项目进度规划图表
      const ctx = document.getElementById('roadmapChart').getContext('2d');
      const roadmapChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['公众查询服务', '用户认证授权', '用户管理', '权限管理', '地质灾害点管理', '风险防范区管理', '操作日志管理', '数据导入导出', '预警信息管理', '多渠道发布'],
          datasets: [{
            label: '计划工期（周）',
            data: [2, 1.5, 1.5, 1.5, 2, 2, 2, 1.5, 1, 2],
            backgroundColor: [
              '#2E86C1', '#2E86C1', 
              '#AED6F1', '#AED6F1', '#AED6F1', '#AED6F1', 
              '#1A5276', '#1A5276', '#1A5276', '#1A5276'
            ],
            borderColor: '#F4F6F7',
            borderWidth: 1,
            borderRadius: 4
          }]
        },
        options: {
          indexAxis: 'y',
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: '主要功能模块开发计划',
              font: {
                size: 12
              }
            },
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return `计划工期：${context.raw}周`;
                }
              }
            }
          },
          scales: {
            x: {
              beginAtZero: true,
              title: {
                display: true,
                text: '工期（周）',
                font: {
                  size: 10
                }
              },
              max: 3,
              ticks: {
                font: {
                  size: 10
                }
              }
            },
            y: {
              ticks: {
                font: {
                  size: 9
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

