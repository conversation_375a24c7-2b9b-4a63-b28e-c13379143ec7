<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实施路线图 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
      .roadmap-page {
        background: var(--gray-100);
        color: var(--gray-800);
        min-height: 100vh;
      }

      .roadmap-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }

      .roadmap-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image:
          linear-gradient(90deg, rgba(255,255,255,0.1) 50%, transparent 50%),
          linear-gradient(rgba(255,255,255,0.1) 50%, transparent 50%);
        background-size: 20px 20px;
        opacity: 0.3;
      }

      .roadmap-content {
        position: relative;
        z-index: 2;
      }

      .roadmap-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }

      .roadmap-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }

      .roadmap-grid {
        display: grid;
        gap: var(--space-6);
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .roadmap-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
      }

      .roadmap-card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-light);
      }

      .roadmap-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-light);
        transform: scaleY(0);
        transition: transform var(--transition-normal);
      }

      .roadmap-card:hover::before {
        transform: scaleY(1);
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-4);
      }

      .card-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 40px;
        text-align: center;
        transition: transform var(--transition-normal);
      }

      .roadmap-card:hover .card-icon {
        transform: scale(1.1);
      }

      .card-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
      }

      .card-body {
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }

      .card-body ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .card-body li {
        margin-bottom: var(--space-3);
        position: relative;
        padding-left: var(--space-5);
      }

      .card-body li:before {
        content: "▶";
        color: var(--primary-light);
        font-size: var(--text-xs);
        position: absolute;
        left: 0;
        top: 2px;
      }

      .highlight-text {
        color: var(--primary-light);
        font-weight: 600;
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 响应式适配 */
      @media (max-width: 479px) {
        .roadmap-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }

        .roadmap-card {
          padding: var(--space-4);
        }
      }

      @media (min-width: 480px) and (max-width: 767px) {
        .roadmap-grid {
          grid-template-columns: 1fr;
        }
      }

      @media (min-width: 768px) and (max-width: 1023px) {
        .roadmap-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (min-width: 1024px) {
        .roadmap-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container roadmap-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section roadmap-left">
          <div class="roadmap-content">
            <h1 class="roadmap-title">实施路线图</h1>
            <p class="roadmap-subtitle">
              分阶段、有步骤地推进平台建设，确保项目顺利实施和成功交付。
            </p>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-section">
          <div class="roadmap-grid">
            <!-- 第一阶段 -->
            <div class="roadmap-card">
              <div class="card-header">
                <i class="card-icon fas fa-play"></i>
                <h3 class="card-title">第一阶段：基础建设</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">系统架构设计</span>：完成技术架构和数据库设计</li>
                  <li><span class="highlight-text">核心功能开发</span>：实现数据管理和查询功能</li>
                  <li><span class="highlight-text">基础数据导入</span>：完成74,215个地质灾害点数据导入</li>
                  <li><span class="highlight-text">预计时间</span>：3-4个月</li>
                </ul>
              </div>
            </div>

            <!-- 第二阶段 -->
            <div class="roadmap-card">
              <div class="card-header">
                <i class="card-icon fas fa-cogs"></i>
                <h3 class="card-title">第二阶段：功能完善</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">预警发布系统</span>：开发多渠道预警发布功能</li>
                  <li><span class="highlight-text">微信公众号集成</span>：实现微信查询和预警推送</li>
                  <li><span class="highlight-text">用户界面优化</span>：完善前端界面和用户体验</li>
                  <li><span class="highlight-text">预计时间</span>：2-3个月</li>
                </ul>
              </div>
            </div>

            <!-- 第三阶段 -->
            <div class="roadmap-card">
              <div class="card-header">
                <i class="card-icon fas fa-shield-alt"></i>
                <h3 class="card-title">第三阶段：安全加固</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">安全防护机制</span>：完善系统安全和权限管理</li>
                  <li><span class="highlight-text">性能优化</span>：提升系统性能和并发处理能力</li>
                  <li><span class="highlight-text">测试验收</span>：全面测试和问题修复</li>
                  <li><span class="highlight-text">预计时间</span>：1-2个月</li>
                </ul>
              </div>
            </div>

            <!-- 第四阶段 -->
            <div class="roadmap-card">
              <div class="card-header">
                <i class="card-icon fas fa-rocket"></i>
                <h3 class="card-title">第四阶段：上线运营</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">系统部署</span>：完成生产环境部署和配置</li>
                  <li><span class="highlight-text">用户培训</span>：开展系统使用培训和推广</li>
                  <li><span class="highlight-text">运维保障</span>：建立运维监控和技术支持体系</li>
                  <li><span class="highlight-text">预计时间</span>：1个月</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="advantages.html" class="nav-btn" aria-label="上一页">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">上一页</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="value_analysis.html" class="nav-btn" aria-label="下一页">
          <span class="mr-2">下一页</span>
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </a>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            window.location.href = 'advantages.html';
            break;
          case 'ArrowRight':
            e.preventDefault();
            window.location.href = 'value_analysis.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
