<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实施路线图 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
      .roadmap-page {
        background: var(--gray-100);
        color: var(--gray-800);
        min-height: 100vh;
      }

      .roadmap-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }

      .roadmap-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image:
          linear-gradient(90deg, rgba(255,255,255,0.1) 50%, transparent 50%),
          linear-gradient(rgba(255,255,255,0.1) 50%, transparent 50%);
        background-size: 20px 20px;
        opacity: 0.3;
      }

      .roadmap-content {
        position: relative;
        z-index: 2;
      }

      .roadmap-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }

      .roadmap-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }

      .roadmap-grid {
        display: grid;
        gap: var(--space-6);
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .roadmap-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
      }

      .roadmap-card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-light);
      }

      .roadmap-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-light);
        transform: scaleY(0);
        transition: transform var(--transition-normal);
      }

      .roadmap-card:hover::before {
        transform: scaleY(1);
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-4);
      }

      .card-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 40px;
        text-align: center;
        transition: transform var(--transition-normal);
      }

      .roadmap-card:hover .card-icon {
        transform: scale(1.1);
      }

      .card-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
      }

      .card-body {
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }

      .card-body ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .card-body li {
        margin-bottom: var(--space-3);
        position: relative;
        padding-left: var(--space-5);
      }

      .card-body li:before {
        content: "▶";
        color: var(--primary-light);
        font-size: var(--text-xs);
        position: absolute;
        left: 0;
        top: 2px;
      }

      .highlight-text {
        color: var(--primary-light);
        font-weight: 600;
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 响应式适配 */
      @media (max-width: 479px) {
        .roadmap-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }

        .roadmap-card {
          padding: var(--space-4);
        }
      }

      @media (min-width: 480px) and (max-width: 767px) {
        .roadmap-grid {
          grid-template-columns: 1fr;
        }
      }

      @media (min-width: 768px) and (max-width: 1023px) {
        .roadmap-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (min-width: 1024px) {
        .roadmap-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container roadmap-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section roadmap-left">
          <div class="roadmap-content">
            <h1 class="roadmap-title">实施路线图</h1>
            <p class="roadmap-subtitle">
              平台建设采用分阶段实施策略，总体周期约4个月（13周），分为三个主要阶段逐步推进，确保项目能够快速见效并持续优化。
            </p>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-section">
          <!-- 项目进度规划图表 -->
          <div class="roadmap-card" style="margin-bottom: 2rem;">
            <div class="card-header">
              <i class="card-icon fas fa-tasks"></i>
              <h3 class="card-title">项目进度规划</h3>
            </div>
            <div class="card-body">
              <div style="height: 300px; margin-top: 1rem;">
                <canvas id="roadmapChart"></canvas>
              </div>
            </div>
          </div>

          <div class="roadmap-grid">
            <!-- 第一阶段 -->
            <div class="roadmap-card">
              <div class="card-header">
                <i class="card-icon fas fa-rocket"></i>
                <h3 class="card-title">第一阶段：公众查询服务快速上线（2周内）</h3>
              </div>
              <div class="card-body">
                <p style="margin-bottom: 1rem;">快速解决公众查询服务缺失的紧迫问题，建立用户对平台的初步信心和认知。</p>
                <div style="background: var(--primary-light); color: var(--white); padding: 0.5rem 1rem; border-radius: var(--radius-md); font-size: var(--text-sm);">
                  <i class="fas fa-flag" style="margin-right: 0.5rem;"></i>里程碑：网站查询服务和微信公众号集成上线
                </div>
              </div>
            </div>

            <!-- 第二阶段 -->
            <div class="roadmap-card">
              <div class="card-header">
                <i class="card-icon fas fa-database"></i>
                <h3 class="card-title">第二阶段：系统管理和数据管理体系建设（3-10周）</h3>
              </div>
              <div class="card-body">
                <p style="margin-bottom: 1rem;">建立完整的系统管理体系和地质灾害数据的信息化管理，为预警发布功能提供支撑。</p>
                <div style="background: var(--primary-light); color: var(--white); padding: 0.5rem 1rem; border-radius: var(--radius-md); font-size: var(--text-sm);">
                  <i class="fas fa-flag" style="margin-right: 0.5rem;"></i>里程碑：用户管理、权限控制和数据管理功能完成
                </div>
              </div>
            </div>

            <!-- 第三阶段 -->
            <div class="roadmap-card">
              <div class="card-header">
                <i class="card-icon fas fa-bullhorn"></i>
                <h3 class="card-title">第三阶段：预警发布机制完善（10-13周）</h3>
              </div>
              <div class="card-body">
                <p style="margin-bottom: 1rem;">建立多渠道预警发布机制，完善系统管理功能，实现平台的完整功能体系。</p>
                <div style="background: var(--primary-light); color: var(--white); padding: 0.5rem 1rem; border-radius: var(--radius-md); font-size: var(--text-sm);">
                  <i class="fas fa-flag" style="margin-right: 0.5rem;"></i>里程碑：系统功能完整性达到100%，系统全面运行
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="advantages.html" class="nav-btn" aria-label="上一页">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">上一页</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="value_analysis.html" class="nav-btn" aria-label="下一页">
          <span class="mr-2">下一页</span>
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </a>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
        initRoadmapChart();
      });

      // 初始化项目进度规划图表
      function initRoadmapChart() {
        const ctx = document.getElementById('roadmapChart').getContext('2d');

        const roadmapChart = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: ['公众查询服务', '用户认证授权', '用户管理', '权限管理', '地质灾害点管理', '风险防范区管理', '操作日志管理', '数据导入导出', '预警信息管理', '多渠道发布'],
            datasets: [{
              label: '计划工期（周）',
              data: [2, 1.5, 1.5, 1.5, 2, 2, 2, 1.5, 1, 2],
              backgroundColor: [
                '#2E86C1', '#2E86C1',
                '#AED6F1', '#AED6F1', '#AED6F1', '#AED6F1',
                '#1A5276', '#1A5276', '#1A5276', '#1A5276'
              ],
              borderColor: '#F4F6F7',
              borderWidth: 1,
              borderRadius: 4
            }]
          },
          options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              title: {
                display: true,
                text: '各功能模块开发时间安排',
                font: {
                  size: 14
                }
              },
              legend: {
                position: 'bottom',
                labels: {
                  font: {
                    size: 12
                  }
                }
              }
            },
            scales: {
              x: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: '工期（周）',
                  font: {
                    size: 12
                  }
                }
              },
              y: {
                ticks: {
                  font: {
                    size: 10
                  }
                }
              }
            }
          }
        });
      }

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            window.location.href = 'advantages.html';
            break;
          case 'ArrowRight':
            e.preventDefault();
            window.location.href = 'value_analysis.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
