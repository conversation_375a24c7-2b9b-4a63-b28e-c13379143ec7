<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      /* 首页特定样式 */
      .hero-container {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        position: relative;
        overflow: hidden;
      }

      .hero-content {
        text-align: center;
        z-index: 2;
        position: relative;
        max-width: 900px;
        margin: 0 auto;
      }

      .hero-title {
        font-size: clamp(2rem, 6vw, 4.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        line-height: 1.2;
        animation: fadeInUp 1s ease-out;
      }

      .hero-subtitle {
        font-size: clamp(1.2rem, 4vw, 2.25rem);
        margin-bottom: var(--space-8);
        opacity: 0.95;
        animation: fadeInUp 1s ease-out 0.2s both;
      }

      .hero-description {
        font-size: clamp(1rem, 2.5vw, 1.5rem);
        margin-bottom: var(--space-8);
        opacity: 0.9;
        line-height: 1.6;
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .hero-footer {
        font-size: clamp(1rem, 2vw, 1.25rem);
        opacity: 0.8;
        animation: fadeInUp 1s ease-out 0.6s both;
      }

      .background-pattern {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.1;
        background-image:
          radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 2px, transparent 2px),
          radial-gradient(circle at 75% 75%, rgba(255,255,255,0.2) 2px, transparent 2px);
        background-size: 50px 50px;
        z-index: 1;
      }

      .floating-elements {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 1;
      }

      .floating-icon {
        position: absolute;
        color: rgba(255, 255, 255, 0.1);
        animation: float 6s ease-in-out infinite;
      }

      .floating-icon:nth-child(1) {
        top: 20%;
        left: 10%;
        font-size: 3rem;
        animation-delay: 0s;
      }

      .floating-icon:nth-child(2) {
        top: 60%;
        right: 15%;
        font-size: 2.5rem;
        animation-delay: 2s;
      }

      .floating-icon:nth-child(3) {
        bottom: 30%;
        left: 20%;
        font-size: 2rem;
        animation-delay: 4s;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes float {
        0%, 100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-20px) rotate(5deg);
        }
      }

      /* 响应式优化 */
      @media (max-width: 479px) {
        .hero-container {
          padding: var(--space-6) var(--space-4);
        }

        .floating-icon {
          display: none; /* 在小屏幕上隐藏装饰元素 */
        }
      }

      @media (min-width: 480px) and (max-width: 767px) {
        .hero-container {
          padding: var(--space-8) var(--space-6);
        }
      }

      @media (min-width: 768px) {
        .hero-container {
          padding: var(--space-12) var(--space-8);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container hero-container full-screen">
      <!-- 背景装饰元素 -->
      <div class="background-pattern"></div>
      <div class="floating-elements">
        <i class="floating-icon fas fa-mountain"></i>
        <i class="floating-icon fas fa-shield-alt"></i>
        <i class="floating-icon fas fa-bell"></i>
      </div>

      <!-- 主要内容 -->
      <div class="hero-content">
        <h1 class="hero-title">茂名市地质灾害预警平台</h1>
        <h2 class="hero-subtitle">茂名市民身边的地质安全守护者</h2>
        <p class="hero-description">
          让地质灾害风险信息触手可及，让安全防护深入人心
        </p>
        <div class="hero-footer">
          <p>茂名市自然资源勘探测绘院</p>
          <p class="text-sm mt-2 opacity-75">2025年7月19日</p>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="catalog.html" class="nav-btn" aria-label="进入目录页面">
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
          <span class="ml-2">开始浏览</span>
        </a>
      </div>
    </div>

    <!-- 增强导航脚本 -->
    <script src="assets/js/enhanced-navigation.js"></script>
    <script>
      // 首页特定的交互增强
      document.addEventListener('DOMContentLoaded', function() {
        // 添加特殊的键盘支持
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            window.location.href = 'catalog.html';
          }
        });

        // 添加点击整个页面进入下一页的功能
        document.addEventListener('click', function(e) {
          // 避免点击导航按钮时触发
          if (!e.target.closest('.navigation')) {
            window.location.href = 'catalog.html';
          }
        });
      });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>

