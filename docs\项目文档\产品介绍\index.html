<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>茂名市地质灾害预警平台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'SourceHanSansCN';
        src: url('https://cdn.jsdelivr.net/npm/source-han-sans-cn@1.001/SubsetOTF/CN/SourceHanSansCN-Bold.otf') format('opentype');
        font-weight: bold;
      }
      @font-face {
        font-family: 'MicrosoftYaHei';
        src: url('https://cdn.jsdelivr.net/npm/microsoft-yahei@1.0.0/Microsoft-YaHei.ttf') format('truetype');
      }
      .slide-container {
        width: 100%;
        max-width: 1280px;
        min-height: 100vh;
        margin: 0 auto;
        background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
        color: #FFFFFF;
        font-family: 'MicrosoftYaHei', sans-serif;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 2rem;
        position: relative;
      }
      .title {
        font-family: 'SourceHanSansCN', sans-serif;
        font-size: clamp(2rem, 5vw, 4.5rem);
        font-weight: bold;
        margin-bottom: 1.5rem;
        text-align: center;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }
      .subtitle {
        font-size: clamp(1.2rem, 3vw, 2.25rem);
        margin-bottom: 2rem;
        text-align: center;
      }
      .description {
        font-size: clamp(1rem, 2vw, 1.5rem);
        max-width: 800px;
        text-align: center;
        margin-bottom: 2rem;
      }
      .background-image {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0.15;
        background-image: url('/home/<USER>/presentation/images/3xWqEVJmn8qn.png');
        background-size: cover;
        background-position: center;
        z-index: -1;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
      }
      .nav-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
      }
      .nav-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
      @media (max-width: 768px) {
        .slide-container {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="background-image"></div>
      <div class="title">茂名市地质灾害预警平台</div>
      <div class="subtitle">茂名市民身边的地质安全守护者</div>
      <div class="description">让地质灾害风险信息触手可及，让安全防护深入人心</div>
      <div class="text-lg md:text-xl mt-4">茂名市自然资源勘探测绘院</div>

      <div class="navigation">
        <a href="catalog.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>
  </body>
</html>

