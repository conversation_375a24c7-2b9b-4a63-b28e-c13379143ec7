<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>茂名市地质灾害预警平台产品介绍 - 目录</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 目录页面特定样式 */
        .catalog-page {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
            min-height: 100vh;
            color: var(--white);
            position: relative;
            overflow-x: hidden;
        }

        .catalog-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.05;
            background-image:
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.3) 2px, transparent 2px),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.3) 2px, transparent 2px);
            background-size: 60px 60px;
            z-index: 1;
        }

        .catalog-content {
            position: relative;
            z-index: 2;
            padding: var(--space-8) 0;
        }

        .catalog-header {
            text-align: center;
            margin-bottom: var(--space-12);
            animation: fadeInDown 1s ease-out;
        }

        .catalog-title {
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 700;
            margin-bottom: var(--space-4);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .catalog-subtitle {
            font-size: clamp(1.2rem, 3vw, 1.8rem);
            margin-bottom: var(--space-2);
            opacity: 0.95;
        }

        .catalog-description {
            font-size: clamp(1rem, 2vw, 1.2rem);
            opacity: 0.85;
        }

        .catalog-grid {
            display: grid;
            gap: var(--space-6);
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }

        .catalog-item {
            background: var(--white);
            color: var(--gray-800);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            text-decoration: none;
            transition: all var(--transition-normal);
            box-shadow: var(--shadow-md);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
            animation: fadeInUp 0.8s ease-out;
        }

        .catalog-item:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-light);
        }

        .catalog-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(46, 134, 193, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .catalog-item:hover::before {
            left: 100%;
        }

        .catalog-item-header {
            display: flex;
            align-items: center;
            margin-bottom: var(--space-4);
        }

        .catalog-item-icon {
            font-size: var(--text-2xl);
            color: var(--primary-light);
            margin-right: var(--space-4);
            width: 40px;
            text-align: center;
        }

        .catalog-item-title {
            font-size: var(--text-xl);
            font-weight: 600;
            color: var(--gray-800);
        }

        .catalog-item-description {
            font-size: var(--text-sm);
            color: var(--gray-600);
            line-height: 1.6;
        }

        .catalog-footer {
            text-align: center;
            margin-top: var(--space-12);
            padding-bottom: var(--space-8);
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        .back-to-home {
            position: fixed;
            top: var(--space-8);
            left: var(--space-8);
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: var(--space-3) var(--space-4);
            border-radius: var(--radius-md);
            text-decoration: none;
            transition: all var(--transition-normal);
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .back-to-home:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式适配 */
        @media (max-width: 479px) {
            .catalog-content {
                padding: var(--space-6) 0;
            }

            .catalog-grid {
                grid-template-columns: 1fr;
                gap: var(--space-4);
                padding: 0 var(--space-3);
            }

            .back-to-home {
                top: var(--space-4);
                left: var(--space-4);
                padding: var(--space-2) var(--space-3);
                font-size: var(--text-sm);
            }
        }

        @media (min-width: 480px) and (max-width: 767px) {
            .catalog-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (min-width: 768px) and (max-width: 1023px) {
            .catalog-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .catalog-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (min-width: 1440px) {
            .catalog-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="catalog-page">
        <!-- 背景装饰 -->
        <div class="catalog-background"></div>

        <!-- 返回首页按钮 -->
        <a href="index.html" class="back-to-home" aria-label="返回首页">
            <i class="fas fa-home" aria-hidden="true"></i>
            <span class="ml-2">首页</span>
        </a>

        <!-- 主要内容 -->
        <div class="catalog-content">
            <!-- 页面标题 -->
            <div class="catalog-header">
                <h1 class="catalog-title">茂名市地质灾害预警平台</h1>
                <p class="catalog-subtitle">产品介绍演示文稿</p>
                <p class="catalog-description">茂名市民身边的地质安全守护者</p>
            </div>

            <!-- 目录网格 -->
            <div class="catalog-grid">
                <a href="overview.html" class="catalog-item" style="animation-delay: 0.1s">
                    <div class="catalog-item-header">
                        <i class="catalog-item-icon fas fa-eye"></i>
                        <h3 class="catalog-item-title">产品概述</h3>
                    </div>
                    <p class="catalog-item-description">介绍产品定位、服务范围、核心价值和产品特色</p>
                </a>

                <a href="market_needs.html" class="catalog-item" style="animation-delay: 0.2s">
                    <div class="catalog-item-header">
                        <i class="catalog-item-icon fas fa-chart-line"></i>
                        <h3 class="catalog-item-title">市场背景与需求</h3>
                    </div>
                    <p class="catalog-item-description">分析市场环境、用户需求、痛点识别和市场机会</p>
                </a>

                <a href="vision_goals.html" class="catalog-item" style="animation-delay: 0.3s">
                    <div class="catalog-item-header">
                        <i class="catalog-item-icon fas fa-bullseye"></i>
                        <h3 class="catalog-item-title">产品愿景与目标</h3>
                    </div>
                    <p class="catalog-item-description">阐述产品愿景、使命和战略目标</p>
                </a>

                <a href="core_features.html" class="catalog-item" style="animation-delay: 0.4s">
                    <div class="catalog-item-header">
                        <i class="catalog-item-icon fas fa-cogs"></i>
                        <h3 class="catalog-item-title">核心功能特性</h3>
                    </div>
                    <p class="catalog-item-description">详细介绍公众查询服务、数据管理、预警发布和系统管理模块</p>
                </a>

                <a href="tech_architecture.html" class="catalog-item" style="animation-delay: 0.5s">
                    <div class="catalog-item-header">
                        <i class="catalog-item-icon fas fa-sitemap"></i>
                        <h3 class="catalog-item-title">技术架构</h3>
                    </div>
                    <p class="catalog-item-description">展示整体架构设计、技术栈选择和基础设施配置</p>
                </a>

                <a href="advantages.html" class="catalog-item" style="animation-delay: 0.6s">
                    <div class="catalog-item-header">
                        <i class="catalog-item-icon fas fa-star"></i>
                        <h3 class="catalog-item-title">产品优势</h3>
                    </div>
                    <p class="catalog-item-description">突出专业化优势、技术优势、用户体验优势和安全优势</p>
                </a>

                <a href="roadmap.html" class="catalog-item" style="animation-delay: 0.7s">
                    <div class="catalog-item-header">
                        <i class="catalog-item-icon fas fa-road"></i>
                        <h3 class="catalog-item-title">实施路线图</h3>
                    </div>
                    <p class="catalog-item-description">介绍总体规划、分阶段实施策略和关键里程碑</p>
                </a>

                <a href="value_analysis.html" class="catalog-item" style="animation-delay: 0.8s">
                    <div class="catalog-item-header">
                        <i class="catalog-item-icon fas fa-chart-pie"></i>
                        <h3 class="catalog-item-title">价值分析</h3>
                    </div>
                    <p class="catalog-item-description">分析产品的社会价值、政府服务价值和经济社会价值</p>
                </a>

                <a href="conclusion.html" class="catalog-item" style="animation-delay: 0.9s">
                    <div class="catalog-item-header">
                        <i class="catalog-item-icon fas fa-lightbulb"></i>
                        <h3 class="catalog-item-title">总结与展望</h3>
                    </div>
                    <p class="catalog-item-description">总结产品核心价值，展望未来发展方向</p>
                </a>
            </div>

            <!-- 页面底部 -->
            <div class="catalog-footer">
                <p class="text-lg">茂名市自然资源勘探测绘院</p>
                <p class="text-sm mt-2 opacity-75">让地质灾害风险信息触手可及，让安全防护深入人心</p>
                <p class="text-xs mt-2 opacity-60">2025年7月19日</p>
            </div>
        </div>
    </div>

    <!-- 交互脚本 -->
    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.opacity = '1';
        });

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                window.location.href = 'index.html';
            }
        });

        // 为目录项添加键盘导航
        const catalogItems = document.querySelectorAll('.catalog-item');
        catalogItems.forEach((item, index) => {
            item.setAttribute('tabindex', '0');
            item.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    item.click();
                }
            });
        });
    </script>

    <style>
        /* 页面加载动画 */
        body {
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }

        /* 焦点样式 */
        .catalog-item:focus {
            outline: 3px solid rgba(255, 255, 255, 0.8);
            outline-offset: 2px;
        }

        .back-to-home:focus {
            outline: 2px solid rgba(255, 255, 255, 0.8);
            outline-offset: 2px;
        }
    </style>
</body>
</html>

