<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>市场背景与需求 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      /* 市场需求页面特定样式 */
      .market-page {
        background: var(--gray-100);
        color: var(--gray-800);
        min-height: 100vh;
      }
      
      .market-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }
      
      .market-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image: 
          radial-gradient(circle at 30% 30%, rgba(255,255,255,0.15) 2px, transparent 2px),
          radial-gradient(circle at 70% 70%, rgba(255,255,255,0.15) 2px, transparent 2px);
        background-size: 40px 40px;
        opacity: 0.5;
      }
      
      .market-content {
        position: relative;
        z-index: 2;
      }
      
      .market-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }
      
      .market-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }
      
      .market-grid {
        display: grid;
        gap: var(--space-6);
        animation: fadeInUp 1s ease-out 0.4s both;
      }
      
      .market-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
      }
      
      .market-card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-light);
      }
      
      .market-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-light);
        transform: scaleY(0);
        transition: transform var(--transition-normal);
      }
      
      .market-card:hover::before {
        transform: scaleY(1);
      }
      
      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-4);
      }
      
      .card-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 40px;
        text-align: center;
        transition: transform var(--transition-normal);
      }
      
      .market-card:hover .card-icon {
        transform: scale(1.1);
      }
      
      .card-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
      }
      
      .card-body {
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }
      
      .card-body ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }
      
      .card-body li {
        margin-bottom: var(--space-3);
        position: relative;
        padding-left: var(--space-5);
      }
      
      .card-body li:before {
        content: "▶";
        color: var(--primary-light);
        font-size: var(--text-xs);
        position: absolute;
        left: 0;
        top: 2px;
      }
      
      .highlight-text {
        color: var(--primary-light);
        font-weight: 600;
      }
      
      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }
      
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      /* 响应式适配 */
      @media (max-width: 479px) {
        .market-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }
        
        .market-card {
          padding: var(--space-4);
        }
      }
      
      @media (min-width: 480px) and (max-width: 767px) {
        .market-grid {
          grid-template-columns: 1fr;
        }
      }
      
      @media (min-width: 768px) and (max-width: 1023px) {
        .market-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
      
      @media (min-width: 1024px) {
        .market-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container market-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section market-left">
          <div class="market-content">
            <h1 class="market-title">市场背景与需求</h1>
            <p class="market-subtitle">
              深入分析地质灾害防治市场环境、用户需求痛点和发展机会，为平台建设提供科学依据。
            </p>
          </div>
        </div>
        
        <!-- 右侧内容区域 -->
        <div class="right-section">
          <div class="market-grid">
            <!-- 市场环境分析 -->
            <div class="market-card">
              <div class="card-header">
                <i class="card-icon fas fa-chart-line"></i>
                <h3 class="card-title">市场环境分析</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">市场规模持续增长</span>：中国地质灾害防治市场规模约200-300亿元，年均增长率4-6%</li>
                  <li><span class="highlight-text">政策驱动强劲</span>：国家防灾减灾政策、数字政府建设提供强有力支持</li>
                  <li><span class="highlight-text">技术发展成熟</span>：物联网、大数据、AI技术推动预警系统智能化升级</li>
                </ul>
              </div>
            </div>
            
            <!-- 用户需求分析 -->
            <div class="market-card">
              <div class="card-header">
                <i class="card-icon fas fa-users"></i>
                <h3 class="card-title">用户需求分析</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">公众服务需求迫切</span>：472万群众缺乏便捷的地质灾害风险查询渠道</li>
                  <li><span class="highlight-text">管理效率亟待提升</span>：当前数据采用离线管理模式，工作效率低下</li>
                  <li><span class="highlight-text">预警覆盖面不足</span>：现有预警发布渠道单一，无法直接向群众发布</li>
                </ul>
              </div>
            </div>
            
            <!-- 痛点识别 -->
            <div class="market-card">
              <div class="card-header">
                <i class="card-icon fas fa-exclamation-triangle"></i>
                <h3 class="card-title">痛点识别</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">信息获取困难</span>：公众查询渠道缺失，市民不知道如何查询风险信息</li>
                  <li><span class="highlight-text">管理方式落后</span>：数据管理缺乏信息化，影响工作效率和数据准确性</li>
                  <li><span class="highlight-text">预警渠道单一</span>：无法直接将预警信息告知群众，影响预警效果</li>
                </ul>
              </div>
            </div>
            
            <!-- 市场机会 -->
            <div class="market-card">
              <div class="card-header">
                <i class="card-icon fas fa-lightbulb"></i>
                <h3 class="card-title">市场机会</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">政策支持力度大</span>：政府对防灾减灾工作高度重视，政策环境良好</li>
                  <li><span class="highlight-text">用户需求明确</span>：用户群体明确，需求迫切，市场定位清晰</li>
                  <li><span class="highlight-text">技术实现可行</span>：基于成熟技术方案，适合轻量化快速实现</li>
                  <li><span class="highlight-text">社会效益显著</span>：具有重要社会价值，能有效提升公众安全防护能力</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="overview.html" class="nav-btn" aria-label="上一页">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">上一页</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="vision_goals.html" class="nav-btn" aria-label="下一页">
          <span class="mr-2">下一页</span>
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </a>
      </div>
    </div>
    
    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });
      
      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            window.location.href = 'overview.html';
            break;
          case 'ArrowRight':
            e.preventDefault();
            window.location.href = 'vision_goals.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });
    </script>
    
    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
