<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术架构 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
      /* 技术架构页面特定样式 */
      .tech-page {
        background: var(--gray-100);
        color: var(--gray-800);
        min-height: 100vh;
      }

      .tech-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }

      .tech-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image:
          repeating-linear-gradient(
            45deg,
            rgba(255,255,255,0.1) 0px,
            rgba(255,255,255,0.1) 2px,
            transparent 2px,
            transparent 20px
          );
        opacity: 0.3;
      }

      .tech-content {
        position: relative;
        z-index: 2;
      }

      .tech-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }

      .tech-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }

      .tech-main {
        display: flex;
        flex-direction: column;
        gap: var(--space-8);
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      /* 架构图表区域 */
      .architecture-section {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-8);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
      }

      .architecture-section:hover {
        border-color: var(--primary-light);
        box-shadow: var(--shadow-xl);
      }

      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-6);
      }

      .section-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 50px;
        text-align: center;
      }

      .section-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
      }

      .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: var(--space-4);
      }

      .chart-canvas {
        max-height: 100%;
        width: 100% !important;
        height: auto !important;
      }

      /* 技术栈网格 */
      .tech-grid {
        display: grid;
        gap: var(--space-6);
      }

      .tech-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
      }

      .tech-card:hover {
        transform: translateY(-4px);
        border-color: var(--primary-light);
        box-shadow: var(--shadow-xl);
      }

      .tech-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-light), var(--primary-color));
        transform: scaleX(0);
        transition: transform var(--transition-normal);
      }

      .tech-card:hover::before {
        transform: scaleX(1);
      }

      .tech-category {
        margin-bottom: var(--space-5);
      }

      .tech-category-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: var(--space-3);
        display: flex;
        align-items: center;
      }

      .tech-category-title::before {
        content: '';
        width: 4px;
        height: 20px;
        background: var(--primary-light);
        margin-right: var(--space-3);
        border-radius: 2px;
      }

      .tech-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .tech-list li {
        margin-bottom: var(--space-2);
        position: relative;
        padding-left: var(--space-5);
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }

      .tech-list li:before {
        content: "▶";
        color: var(--primary-light);
        font-size: var(--text-xs);
        position: absolute;
        left: 0;
        top: 2px;
      }

      .tech-highlight {
        color: var(--primary-light);
        font-weight: 600;
      }

      /* 架构层级可视化 */
      .architecture-layers {
        display: flex;
        flex-direction: column;
        gap: var(--space-4);
        margin-top: var(--space-6);
      }

      .arch-layer {
        background: linear-gradient(135deg, var(--gray-50), var(--white));
        border: 2px solid var(--gray-200);
        border-radius: var(--radius-md);
        padding: var(--space-4);
        text-align: center;
        transition: all var(--transition-normal);
        position: relative;
      }

      .arch-layer:hover {
        border-color: var(--primary-light);
        background: linear-gradient(135deg, var(--secondary-color), var(--white));
      }

      .arch-layer-title {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: var(--space-2);
      }

      .arch-layer-desc {
        font-size: var(--text-sm);
        color: var(--gray-600);
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 响应式适配 */
      @media (max-width: 479px) {
        .tech-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }

        .tech-card {
          padding: var(--space-4);
        }

        .chart-container {
          height: 250px;
        }

        .architecture-section {
          padding: var(--space-4);
        }
      }

      @media (min-width: 480px) and (max-width: 767px) {
        .tech-grid {
          grid-template-columns: 1fr;
        }

        .chart-container {
          height: 280px;
        }
      }

      @media (min-width: 768px) and (max-width: 1023px) {
        .tech-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (min-width: 1024px) {
        .tech-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container tech-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section tech-left">
          <div class="tech-content">
            <h1 class="tech-title">技术架构</h1>
            <p class="tech-subtitle">
              茂名市地质灾害预警平台采用现代化的三层架构设计，包括表现层、业务逻辑层和数据存储层，确保系统的可扩展性、可维护性和安全性。
            </p>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-section">
          <div class="tech-main">
            <!-- 架构图表区域 -->
            <div class="architecture-section">
              <div class="section-header">
                <i class="section-icon fas fa-sitemap"></i>
                <h3 class="section-title">整体架构设计</h3>
              </div>

              <div class="chart-container">
                <canvas id="architectureChart" class="chart-canvas"></canvas>
              </div>

              <!-- 架构层级可视化 -->
              <div class="architecture-layers">
                <div class="arch-layer">
                  <div class="arch-layer-title">表现层 (Presentation Layer)</div>
                  <div class="arch-layer-desc">Vue3 + Element Plus + Vite</div>
                </div>
                <div class="arch-layer">
                  <div class="arch-layer-title">业务逻辑层 (Business Logic Layer)</div>
                  <div class="arch-layer-desc">Python FastAPI + Nginx</div>
                </div>
                <div class="arch-layer">
                  <div class="arch-layer-title">数据存储层 (Data Storage Layer)</div>
                  <div class="arch-layer-desc">MySQL 8.0 + MongoDB 6.0</div>
                </div>
              </div>
            </div>

            <!-- 技术栈网格 -->
            <div class="tech-grid">
              <!-- 后端技术栈 -->
              <div class="tech-card">
                <div class="section-header">
                  <i class="section-icon fas fa-server"></i>
                  <h3 class="section-title">后端技术栈</h3>
                </div>

                <div class="tech-category">
                  <div class="tech-category-title">核心框架</div>
                  <ul class="tech-list">
                    <li><span class="tech-highlight">Python FastAPI</span>：现代化的高性能Web框架</li>
                    <li><span class="tech-highlight">Nginx</span>：Web服务器和反向代理服务器</li>
                  </ul>
                </div>

                <div class="tech-category">
                  <div class="tech-category-title">数据存储</div>
                  <ul class="tech-list">
                    <li><span class="tech-highlight">MySQL 8.0</span>：关系型数据库，存储业务数据</li>
                    <li><span class="tech-highlight">MongoDB 6.0</span>：文档数据库，存储GEO矢量数据</li>
                  </ul>
                </div>
              </div>

              <!-- 前端技术栈 -->
              <div class="tech-card">
                <div class="section-header">
                  <i class="section-icon fas fa-code"></i>
                  <h3 class="section-title">前端技术栈</h3>
                </div>

                <div class="tech-category">
                  <div class="tech-category-title">开发框架</div>
                  <ul class="tech-list">
                    <li><span class="tech-highlight">Vue3</span>：现代化的前端框架</li>
                    <li><span class="tech-highlight">Element Plus</span>：基于Vue3的组件库</li>
                    <li><span class="tech-highlight">Vite</span>：下一代前端构建工具</li>
                  </ul>
                </div>

                <div class="tech-category">
                  <div class="tech-category-title">第三方服务</div>
                  <ul class="tech-list">
                    <li><span class="tech-highlight">天地图API</span>：国家地理信息公共服务平台</li>
                    <li><span class="tech-highlight">微信公众号API</span>：腾讯官方API</li>
                    <li><span class="tech-highlight">短信服务API</span>：运营商短信服务</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="core_features.html" class="nav-btn" aria-label="上一页">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">上一页</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="advantages.html" class="nav-btn" aria-label="下一页">
          <span class="mr-2">下一页</span>
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </a>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
        initArchitectureChart();
      });

      // 初始化架构图表
      function initArchitectureChart() {
        const ctx = document.getElementById('architectureChart').getContext('2d');

        const architectureChart = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: ['表现层', '业务逻辑层', '数据存储层'],
            datasets: [{
              label: '架构层级',
              data: [1, 1, 1],
              backgroundColor: [
                '#AED6F1',
                '#2E86C1',
                '#1A5276'
              ],
              borderColor: [
                '#AED6F1',
                '#2E86C1',
                '#1A5276'
              ],
              borderWidth: 1,
              barPercentage: 0.8
            }]
          },
          options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const labels = [
                      'Vue3 + Element Plus + 响应式设计',
                      'Python FastAPI + Nginx + JWT认证',
                      'MySQL(业务数据) + MongoDB(GEO数据)'
                    ];
                    return labels[context.dataIndex];
                  }
                }
              }
            },
            scales: {
              x: {
                display: false,
                grid: {
                  display: false
                }
              },
              y: {
                grid: {
                  display: false
                },
                ticks: {
                  font: {
                    size: 12,
                    weight: 'bold'
                  }
                }
              }
            }
          }
        });
      }

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            window.location.href = 'core_features.html';
            break;
          case 'ArrowRight':
            e.preventDefault();
            window.location.href = 'advantages.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
