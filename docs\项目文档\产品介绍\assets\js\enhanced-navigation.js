/**
 * 茂名市地质灾害预警平台 - 增强导航和交互脚本
 * 创建日期: 2025-07-19
 * 版本: 1.0
 * 描述: 提供增强的导航体验、键盘支持、触摸手势等功能
 */

class EnhancedNavigation {
  constructor() {
    this.currentPage = this.getCurrentPageName();
    this.pages = [
      'index.html',
      'catalog.html', 
      'overview.html',
      'market_needs.html',
      'vision_goals.html',
      'core_features.html',
      'tech_architecture.html',
      'advantages.html',
      'roadmap.html',
      'value_analysis.html',
      'conclusion.html'
    ];
    this.currentIndex = this.pages.indexOf(this.currentPage);
    
    this.init();
  }
  
  init() {
    this.setupPageLoadAnimation();
    this.setupBackToTop();
    this.setupProgressIndicator();
    this.setupBreadcrumb();
    this.setupKeyboardNavigation();
    this.setupTouchGestures();
    this.setupGestureHints();
    this.setupKeyboardHints();
    this.setupAccessibility();
  }
  
  getCurrentPageName() {
    const path = window.location.pathname;
    const filename = path.split('/').pop();
    return filename || 'index.html';
  }
  
  setupPageLoadAnimation() {
    document.addEventListener('DOMContentLoaded', () => {
      // 页面加载动画
      setTimeout(() => {
        document.body.style.opacity = '1';
      }, 100);
      
      // 添加页面加载完成的类
      document.body.classList.add('page-loaded');
    });
  }
  
  setupBackToTop() {
    // 创建返回顶部按钮
    const backToTop = document.createElement('button');
    backToTop.className = 'back-to-top';
    backToTop.innerHTML = '<i class="fas fa-arrow-up"></i>';
    backToTop.setAttribute('aria-label', '返回顶部');
    document.body.appendChild(backToTop);
    
    // 滚动监听
    let scrollTimeout;
    window.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        if (window.scrollY > 300) {
          backToTop.classList.add('visible');
        } else {
          backToTop.classList.remove('visible');
        }
      }, 100);
    });
    
    // 点击返回顶部
    backToTop.addEventListener('click', () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  }
  
  setupProgressIndicator() {
    // 创建进度指示器
    const progressIndicator = document.createElement('div');
    progressIndicator.className = 'progress-indicator';
    progressIndicator.innerHTML = '<div class="progress-bar"></div>';
    document.body.appendChild(progressIndicator);
    
    const progressBar = progressIndicator.querySelector('.progress-bar');
    
    // 更新进度
    const updateProgress = () => {
      const progress = (this.currentIndex / (this.pages.length - 1)) * 100;
      progressBar.style.width = `${progress}%`;
    };
    
    updateProgress();
  }
  
  setupBreadcrumb() {
    // 创建面包屑导航
    const breadcrumb = document.createElement('nav');
    breadcrumb.className = 'breadcrumb';
    breadcrumb.setAttribute('aria-label', '面包屑导航');
    
    const pageNames = {
      'index.html': '首页',
      'catalog.html': '目录',
      'overview.html': '产品概述',
      'market_needs.html': '市场背景与需求',
      'vision_goals.html': '产品愿景与目标',
      'core_features.html': '核心功能特性',
      'tech_architecture.html': '技术架构',
      'advantages.html': '产品优势',
      'roadmap.html': '实施路线图',
      'value_analysis.html': '价值分析',
      'conclusion.html': '总结与展望'
    };
    
    let breadcrumbHTML = '<a href="index.html">首页</a>';
    if (this.currentPage !== 'index.html') {
      breadcrumbHTML += '<span class="separator">></span>';
      if (this.currentPage !== 'catalog.html') {
        breadcrumbHTML += '<a href="catalog.html">目录</a>';
        breadcrumbHTML += '<span class="separator">></span>';
      }
      breadcrumbHTML += `<span class="current">${pageNames[this.currentPage] || '未知页面'}</span>`;
    }
    
    breadcrumb.innerHTML = breadcrumbHTML;
    document.body.appendChild(breadcrumb);
  }
  
  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // 防止在输入框中触发导航
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
      }
      
      switch(e.key) {
        case 'ArrowLeft':
        case 'ArrowUp':
          e.preventDefault();
          this.navigateToPrevious();
          break;
        case 'ArrowRight':
        case 'ArrowDown':
          e.preventDefault();
          this.navigateToNext();
          break;
        case 'Home':
          e.preventDefault();
          window.location.href = 'index.html';
          break;
        case 'End':
          e.preventDefault();
          window.location.href = 'conclusion.html';
          break;
        case 'Escape':
          e.preventDefault();
          window.location.href = 'catalog.html';
          break;
        case '?':
          e.preventDefault();
          this.toggleKeyboardHints();
          break;
      }
    });
  }
  
  setupTouchGestures() {
    let startX = 0;
    let startY = 0;
    let startTime = 0;
    
    document.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
      startTime = Date.now();
    }, { passive: true });
    
    document.addEventListener('touchend', (e) => {
      if (!startX || !startY) return;
      
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      const endTime = Date.now();
      
      const diffX = startX - endX;
      const diffY = startY - endY;
      const timeDiff = endTime - startTime;
      
      // 检测快速滑动手势
      if (timeDiff < 300 && Math.abs(diffX) > 50 && Math.abs(diffX) > Math.abs(diffY)) {
        if (diffX > 0) {
          // 左滑 - 下一页
          this.navigateToNext();
        } else {
          // 右滑 - 上一页
          this.navigateToPrevious();
        }
      }
      
      startX = 0;
      startY = 0;
    }, { passive: true });
  }
  
  setupGestureHints() {
    // 检测是否为触摸设备
    if ('ontouchstart' in window) {
      const gestureHint = document.createElement('div');
      gestureHint.className = 'gesture-hint';
      gestureHint.textContent = '左右滑动切换页面';
      document.body.appendChild(gestureHint);
      
      // 显示提示3秒后隐藏
      setTimeout(() => {
        gestureHint.classList.add('visible');
        setTimeout(() => {
          gestureHint.classList.remove('visible');
        }, 3000);
      }, 1000);
    }
  }
  
  setupKeyboardHints() {
    const keyboardHint = document.createElement('div');
    keyboardHint.className = 'keyboard-hint';
    keyboardHint.innerHTML = `
      <h4>键盘快捷键</h4>
      <ul>
        <li><span>←/→</span> <span class="key">切换页面</span></li>
        <li><span>Home</span> <span class="key">首页</span></li>
        <li><span>End</span> <span class="key">最后页</span></li>
        <li><span>Esc</span> <span class="key">目录</span></li>
        <li><span>?</span> <span class="key">帮助</span></li>
      </ul>
    `;
    document.body.appendChild(keyboardHint);
    
    this.keyboardHint = keyboardHint;
  }
  
  setupAccessibility() {
    // 为所有交互元素添加焦点样式
    const style = document.createElement('style');
    style.textContent = `
      *:focus {
        outline: 2px solid var(--primary-light);
        outline-offset: 2px;
      }
      
      .nav-btn:focus,
      .back-to-top:focus {
        outline: 3px solid rgba(46, 134, 193, 0.8);
        outline-offset: 2px;
      }
    `;
    document.head.appendChild(style);
  }
  
  navigateToPrevious() {
    if (this.currentIndex > 0) {
      window.location.href = this.pages[this.currentIndex - 1];
    }
  }
  
  navigateToNext() {
    if (this.currentIndex < this.pages.length - 1) {
      window.location.href = this.pages[this.currentIndex + 1];
    }
  }
  
  toggleKeyboardHints() {
    if (this.keyboardHint.classList.contains('visible')) {
      this.keyboardHint.classList.remove('visible');
    } else {
      this.keyboardHint.classList.add('visible');
      // 5秒后自动隐藏
      setTimeout(() => {
        this.keyboardHint.classList.remove('visible');
      }, 5000);
    }
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  new EnhancedNavigation();
});

// 导出供其他脚本使用
window.EnhancedNavigation = EnhancedNavigation;
