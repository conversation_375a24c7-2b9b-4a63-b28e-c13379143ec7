<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>谢谢观看 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      /* 结束页面特定样式 */
      .ending-container {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        position: relative;
        overflow: hidden;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: var(--white);
      }

      .ending-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
          radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 2px, transparent 2px),
          radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 2px, transparent 2px);
        background-size: 60px 60px;
        opacity: 0.5;
        animation: float 20s ease-in-out infinite;
      }

      .ending-content {
        text-align: center;
        z-index: 2;
        position: relative;
        max-width: 800px;
        margin: 0 auto;
        padding: var(--space-8);
      }

      .main-title {
        font-size: clamp(3rem, 8vw, 6rem);
        font-weight: 700;
        margin-bottom: var(--space-4);
        text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
        animation: fadeInUp 1s ease-out;
      }

      .subtitle {
        font-size: clamp(1.2rem, 3vw, 1.8rem);
        margin-bottom: var(--space-8);
        opacity: 0.9;
        font-style: italic;
        animation: fadeInUp 1s ease-out 0.2s both;
      }

      .thank-you-message {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        margin-bottom: var(--space-8);
        opacity: 0.9;
        line-height: 1.6;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .contact-section {
        margin-bottom: var(--space-8);
        animation: fadeInUp 1s ease-out 0.6s both;
      }

      .contact-title {
        font-size: clamp(1.3rem, 3vw, 1.8rem);
        font-weight: 600;
        margin-bottom: var(--space-6);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-3);
      }

      .contact-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-4);
        max-width: 800px;
        margin: 0 auto;
      }

      .contact-item {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        padding: var(--space-3);
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-md);
        backdrop-filter: blur(10px);
      }

      .contact-icon {
        font-size: 1.2rem;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
      }

      .contact-label {
        font-weight: 600;
        font-size: var(--text-sm);
        opacity: 0.8;
        min-width: 60px;
      }

      .contact-value {
        font-size: var(--text-sm);
        opacity: 0.9;
      }

      .slogan {
        font-size: clamp(1.1rem, 2.5vw, 1.5rem);
        font-weight: 600;
        line-height: 1.6;
        opacity: 0.95;
        animation: fadeInUp 1s ease-out 0.8s both;
      }

      .ending-actions {
        display: flex;
        gap: var(--space-6);
        justify-content: center;
        flex-wrap: wrap;
        animation: fadeInUp 1s ease-out 0.8s both;
      }

      .ending-btn {
        background: rgba(255, 255, 255, 0.2);
        color: var(--white);
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: var(--space-4) var(--space-8);
        border-radius: var(--radius-lg);
        text-decoration: none;
        font-size: var(--text-lg);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--space-3);
        transition: all var(--transition-normal);
        backdrop-filter: blur(10px);
        min-height: 60px;
      }

      .ending-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
      }

      .floating-icons {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 1;
      }

      .floating-icon {
        position: absolute;
        color: rgba(255, 255, 255, 0.1);
        animation: floatIcon 8s ease-in-out infinite;
      }

      .floating-icon:nth-child(1) {
        top: 10%;
        left: 10%;
        font-size: 4rem;
        animation-delay: 0s;
      }

      .floating-icon:nth-child(2) {
        top: 20%;
        right: 15%;
        font-size: 3rem;
        animation-delay: 2s;
      }

      .floating-icon:nth-child(3) {
        bottom: 20%;
        left: 15%;
        font-size: 3.5rem;
        animation-delay: 4s;
      }

      .floating-icon:nth-child(4) {
        bottom: 10%;
        right: 10%;
        font-size: 2.5rem;
        animation-delay: 6s;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(50px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes float {
        0%, 100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-20px) rotate(2deg);
        }
      }

      @keyframes floatIcon {
        0%, 100% {
          transform: translateY(0px) rotate(0deg);
        }
        25% {
          transform: translateY(-15px) rotate(5deg);
        }
        50% {
          transform: translateY(-30px) rotate(0deg);
        }
        75% {
          transform: translateY(-15px) rotate(-5deg);
        }
      }

      /* 响应式适配 */
      @media (max-width: 479px) {
        .ending-content {
          padding: var(--space-6) var(--space-4);
        }

        .ending-actions {
          flex-direction: column;
          gap: var(--space-4);
        }

        .ending-btn {
          padding: var(--space-3) var(--space-6);
          font-size: var(--text-base);
          min-height: 50px;
        }

        .floating-icon {
          display: none; /* 在小屏幕上隐藏装饰元素 */
        }
      }

      @media (min-width: 480px) and (max-width: 767px) {
        .ending-content {
          padding: var(--space-8) var(--space-6);
        }

        .ending-actions {
          gap: var(--space-4);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container ending-container full-screen">
      <!-- 背景装饰 -->
      <div class="ending-background"></div>
      <div class="floating-icons">
        <i class="floating-icon fas fa-mountain"></i>
        <i class="floating-icon fas fa-shield-alt"></i>
        <i class="floating-icon fas fa-bell"></i>
        <i class="floating-icon fas fa-heart"></i>
      </div>

      <!-- 主要内容 -->
      <div class="ending-content">
        <div class="main-title">感谢聆听</div>
        <div class="subtitle">Thank You for Your Attention</div>

        <div class="thank-you-message">
          感谢您对茂名市地质灾害预警平台的关注与支持。我们将继续致力于为茂名市民提供更加专业、便民、高效的地质灾害防治信息服务，共同守护我们美丽的家园。
        </div>

        <div class="contact-section">
          <div class="contact-title">
            <i class="fas fa-address-card"></i>联系我们
          </div>
          <div class="contact-info">
            <div class="contact-item">
              <div class="contact-icon">
                <i class="fas fa-building"></i>
              </div>
              <div class="contact-label">开发单位</div>
              <div class="contact-value">茂名市自然资源勘探测绘院</div>
            </div>

            <div class="contact-item">
              <div class="contact-icon">
                <i class="fas fa-map-marker-alt"></i>
              </div>
              <div class="contact-label">地址</div>
              <div class="contact-value">广东省茂名市</div>
            </div>

            <div class="contact-item">
              <div class="contact-icon">
                <i class="fas fa-envelope"></i>
              </div>
              <div class="contact-label">邮箱</div>
              <div class="contact-value"><EMAIL></div>
            </div>

            <div class="contact-item">
              <div class="contact-icon">
                <i class="fas fa-globe"></i>
              </div>
              <div class="contact-label">网站</div>
              <div class="contact-value">www.maoming-geo-warning.gov.cn</div>
            </div>
          </div>
        </div>

        <div class="slogan">
          让地质灾害风险信息触手可及<br>
          让安全防护深入人心
        </div>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
          case 'Backspace':
            e.preventDefault();
            window.location.href = 'conclusion.html';
            break;
          case 'Home':
          case 'Enter':
          case ' ':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });

      // 添加点击整个页面返回首页的功能
      document.addEventListener('click', function(e) {
        // 避免点击按钮时触发
        if (!e.target.closest('.ending-btn')) {
          window.location.href = 'index.html';
        }
      });

      // 添加触摸滑动支持
      let startX = 0;
      let startY = 0;

      document.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
      }, { passive: true });

      document.addEventListener('touchend', function(e) {
        if (!startX || !startY) return;

        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;

        const diffX = startX - endX;
        const diffY = startY - endY;

        // 检测右滑手势 - 返回上一页
        if (Math.abs(diffX) > Math.abs(diffY) && diffX < -50) {
          window.location.href = 'conclusion.html';
        }
        // 检测上滑手势 - 返回首页
        else if (Math.abs(diffY) > Math.abs(diffX) && diffY > 50) {
          window.location.href = 'index.html';
        }

        startX = 0;
        startY = 0;
      }, { passive: true });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
