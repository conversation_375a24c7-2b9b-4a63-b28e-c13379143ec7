<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>感谢聆听 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      /* 结束页面样式 - 与首页风格一致 */
      .hero-container {
        background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
        position: relative;
        overflow: hidden;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
      }

      .hero-content {
        text-align: center;
        z-index: 2;
        position: relative;
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
      }

      .hero-title {
        font-size: clamp(2rem, 6vw, 4.5rem);
        font-weight: 700;
        margin-bottom: 2rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        line-height: 1.2;
        animation: fadeInUp 1s ease-out;
      }

      .hero-description {
        font-size: clamp(1rem, 2.5vw, 1.5rem);
        margin-bottom: 3rem;
        opacity: 0.9;
        line-height: 1.6;
        animation: fadeInUp 1s ease-out 0.2s both;
      }

      .contact-info {
        margin-bottom: 2rem;
        opacity: 0.9;
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .contact-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.8rem;
        margin-bottom: 1rem;
        font-size: clamp(0.9rem, 2vw, 1.2rem);
      }

      .contact-item i {
        color: rgba(255, 255, 255, 0.8);
        width: 20px;
        text-align: center;
      }

      .hero-footer {
        font-size: clamp(1rem, 2vw, 1.25rem);
        opacity: 0.8;
        animation: fadeInUp 1s ease-out 0.6s both;
        margin-top: 1rem;
      }

      .background-pattern {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.1;
        background-image:
          radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 2px, transparent 2px),
          radial-gradient(circle at 75% 75%, rgba(255,255,255,0.2) 2px, transparent 2px);
        background-size: 50px 50px;
        z-index: 1;
      }

      .floating-elements {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 1;
      }

      .floating-icon {
        position: absolute;
        color: rgba(255, 255, 255, 0.1);
        animation: float 6s ease-in-out infinite;
      }

      .floating-icon:nth-child(1) {
        top: 20%;
        left: 10%;
        font-size: 3rem;
        animation-delay: 0s;
      }

      .floating-icon:nth-child(2) {
        top: 60%;
        right: 15%;
        font-size: 2.5rem;
        animation-delay: 2s;
      }

      .floating-icon:nth-child(3) {
        bottom: 30%;
        left: 20%;
        font-size: 2rem;
        animation-delay: 4s;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes float {
        0%, 100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-20px);
        }
      }

      /* 响应式适配 */
      @media (max-width: 768px) {
        .hero-content {
          padding: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container hero-container full-screen">
      <!-- 背景装饰 -->
      <div class="background-pattern"></div>
      <div class="floating-elements">
        <i class="floating-icon fas fa-heart"></i>
        <i class="floating-icon fas fa-handshake"></i>
        <i class="floating-icon fas fa-star"></i>
      </div>

      <!-- 主要内容 -->
      <div class="hero-content">
        <h1 class="hero-title">感谢聆听</h1>
        <p class="hero-description">
          感谢您对茂名市地质灾害预警平台的关注与支持
        </p>
        <div class="contact-info">
          <div class="contact-item">
            <i class="fas fa-building"></i>
            <span>茂名市自然资源勘探测绘院</span>
          </div>
          <div class="contact-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>广东省茂名市</span>
          </div>
        </div>
        <p class="hero-footer">
          让地质灾害风险信息触手可及<br>
          让安全防护深入人心
        </p>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
          case 'Backspace':
            e.preventDefault();
            window.location.href = 'conclusion.html';
            break;
          case 'Home':
          case 'Enter':
          case ' ':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });

      // 添加点击整个页面返回首页的功能
      document.addEventListener('click', function(e) {
        window.location.href = 'index.html';
      });

      // 添加触摸滑动支持
      let startX = 0;
      let startY = 0;

      document.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
      }, { passive: true });

      document.addEventListener('touchend', function(e) {
        if (!startX || !startY) return;

        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;

        const diffX = startX - endX;
        const diffY = startY - endY;

        // 检测右滑手势 - 返回上一页
        if (Math.abs(diffX) > Math.abs(diffY) && diffX < -50) {
          window.location.href = 'conclusion.html';
        }
        // 检测上滑手势 - 返回首页
        else if (Math.abs(diffY) > Math.abs(diffX) && diffY > 50) {
          window.location.href = 'index.html';
        }

        startX = 0;
        startY = 0;
      }, { passive: true });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
