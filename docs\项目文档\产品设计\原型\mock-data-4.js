/**
 * 茂名市地质灾害预警平台 - 模拟数据
 * 创建日期：2025-07-17
 * 说明：包含预警信息、地质灾害点、风险防范区等模拟数据
 */

// 预警数据
var warningData = [
    {
        id: 1,
        title: "茂名市地质灾害气象风险预警信息",
        level: 4,
        levelName: "蓝色",
        levelDesc: "四级",
        status: "生效",
        content: "预计未来24小时，信宜市：合水镇、钱排镇、新宝镇、大成镇；高州市：大坡镇、马贵镇、古丁镇、深镇镇；电白区:罗坑镇、望夫镇、那霍镇，地质灾害气象风险等级达4级(蓝色）。降雨致地质灾害发生有一定风险，注意地灾隐患（风险）点附近的户外作业安全。提醒隐患（风险）点附近居民密切关注预警预报，远离危险体、住前不住后、住上不住下。若出现人员伤亡，要及时报告当地政府、自然资源和应急部门，并拨打市自然资源局地质灾害值班电话0668-2796868速报灾情。2025年6月26日17时【茂名市自然资源局、茂名市气象局联合发布】。",
        publishTime: "2025-06-26 17:00",
        areas: ["信宜市", "高州市", "电白区"],
        towns: ["合水镇", "钱排镇", "新宝镇", "大成镇", "大坡镇", "马贵镇", "古丁镇", "深镇镇", "罗坑镇", "望夫镇", "那霍镇"]
    },
    {
        id: 2,
        title: "茂名市地质灾害气象风险预警信息",
        level: 3,
        levelName: "黄色",
        levelDesc: "三级",
        status: "失效",
        content: "预计未来24小时，信宜市：合水镇、钱排镇；高州市：大坡镇、马贵镇，地质灾害气象风险等级达3级(黄色）。降雨致地质灾害发生风险较高，请加强防范。",
        publishTime: "2025-06-25 14:30",
        areas: ["信宜市", "高州市"],
        towns: ["合水镇", "钱排镇", "大坡镇", "马贵镇"]
    },
    {
        id: 3,
        title: "茂名市地质灾害气象风险预警信息",
        level: 2,
        levelName: "橙色",
        levelDesc: "二级",
        status: "失效",
        content: "预计未来24小时，信宜市：合水镇，地质灾害气象风险等级达2级(橙色）。降雨致地质灾害发生风险高，请高度重视防范工作。",
        publishTime: "2025-06-24 09:15",
        areas: ["信宜市"],
        towns: ["合水镇"]
    },
    {
        id: 4,
        title: "茂名市地质灾害气象风险预警信息",
        level: 1,
        levelName: "红色",
        levelDesc: "一级",
        status: "失效",
        content: "预计未来12小时，信宜市：合水镇、钱排镇，地质灾害气象风险等级达1级(红色）。降雨致地质灾害发生风险极高，请立即采取防范措施。",
        publishTime: "2025-06-23 20:30",
        areas: ["信宜市"],
        towns: ["合水镇", "钱排镇"]
    },
    {
        id: 5,
        title: "茂名市地质灾害气象风险预警信息",
        level: 4,
        levelName: "蓝色",
        levelDesc: "四级",
        status: "失效",
        content: "预计未来24小时，电白区：罗坑镇、望夫镇，地质灾害气象风险等级达4级(蓝色）。",
        publishTime: "2025-06-22 16:00",
        areas: ["电白区"],
        towns: ["罗坑镇", "望夫镇"]
    },
    {
        id: 6,
        title: "茂名市地质灾害气象风险预警信息",
        level: 3,
        levelName: "黄色",
        levelDesc: "三级",
        status: "失效",
        content: "预计未来24小时，高州市：大坡镇、马贵镇、古丁镇，地质灾害气象风险等级达3级(黄色）。",
        publishTime: "2025-06-21 11:45",
        areas: ["高州市"],
        towns: ["大坡镇", "马贵镇", "古丁镇"]
    },
    {
        id: 7,
        title: "茂名市地质灾害气象风险预警信息",
        level: 4,
        levelName: "蓝色",
        levelDesc: "四级",
        status: "失效",
        content: "预计未来24小时，化州市：杨梅镇、平定镇，地质灾害气象风险等级达4级(蓝色）。",
        publishTime: "2025-06-20 14:20",
        areas: ["化州市"],
        towns: ["杨梅镇", "平定镇"]
    },
    {
        id: 8,
        title: "茂名市地质灾害气象风险预警信息",
        level: 2,
        levelName: "橙色",
        levelDesc: "二级",
        status: "失效",
        content: "预计未来24小时，信宜市：新宝镇、大成镇，地质灾害气象风险等级达2级(橙色）。",
        publishTime: "2025-06-19 08:30",
        areas: ["信宜市"],
        towns: ["新宝镇", "大成镇"]
    },
    {
        id: 9,
        title: "茂名市地质灾害气象风险预警信息",
        level: 3,
        levelName: "黄色",
        levelDesc: "三级",
        status: "失效",
        content: "预计未来24小时，电白区：那霍镇，地质灾害气象风险等级达3级(黄色）。",
        publishTime: "2025-06-18 19:15",
        areas: ["电白区"],
        towns: ["那霍镇"]
    },
    {
        id: 10,
        title: "茂名市地质灾害气象风险预警信息",
        level: 4,
        levelName: "蓝色",
        levelDesc: "四级",
        status: "失效",
        content: "预计未来24小时，茂南区：公馆镇、袂花镇，地质灾害气象风险等级达4级(蓝色）。",
        publishTime: "2025-06-17 13:00",
        areas: ["茂南区"],
        towns: ["公馆镇", "袂花镇"]
    },
    {
        id: 11,
        title: "茂名市地质灾害气象风险预警信息",
        level: 3,
        levelName: "黄色",
        levelDesc: "三级",
        status: "失效",
        content: "预计未来24小时，高州市：深镇镇，地质灾害气象风险等级达3级(黄色）。",
        publishTime: "2025-06-16 10:45",
        areas: ["高州市"],
        towns: ["深镇镇"]
    },
    {
        id: 12,
        title: "茂名市地质灾害气象风险预警信息",
        level: 4,
        levelName: "蓝色",
        levelDesc: "四级",
        status: "失效",
        content: "预计未来24小时，化州市：中垌镇、林尘镇，地质灾害气象风险等级达4级(蓝色）。",
        publishTime: "2025-06-15 15:30",
        areas: ["化州市"],
        towns: ["中垌镇", "林尘镇"]
    }
];

// 镇街坐标数据（示例）
var townCoordinates = {
    "合水镇": [110.9234, 22.3567],
    "钱排镇": [110.8456, 22.4123],
    "新宝镇": [110.7890, 22.3890],
    "大成镇": [110.8234, 22.3456],
    "朱砂镇": [110.8567, 22.3789],
    "大坡镇": [110.6789, 22.2345],
    "马贵镇": [110.7123, 22.2678],
    "古丁镇": [110.6456, 22.2890],
    "深镇镇": [110.6890, 22.3123],
    "荷花镇": [110.8525, 21.6000],
    "罗坑镇": [110.5234, 22.1567],
    "望夫镇": [110.5678, 22.1890],
    "那霍镇": [110.5890, 22.2123]
};

// 区县镇街数据
var countyTowns = {
    'maonan': ['公馆镇', '袂花镇', '金塘镇', '镇盛镇'],
    'dianbai': ['罗坑镇', '望夫镇', '那霍镇', '沙琅镇'],
    'gaozhou': ['大坡镇', '马贵镇', '古丁镇', '深镇镇', '荷花镇'],
    'huazhou': ['杨梅镇', '平定镇', '中垌镇', '林尘镇'],
    'xinyi': ['合水镇', '钱排镇', '新宝镇', '大成镇', '朱砂镇']
};

// 地质灾害点数据
var disasterPoints = [
    {lng: 110.9255, lat: 21.6687, name: "茂名市区地质灾害点1", risk: "高风险", type: "滑坡"},
    {lng: 110.8855, lat: 21.6287, name: "茂南区地质灾害点1", risk: "中风险", type: "泥石流"},
    {lng: 111.0255, lat: 21.7087, name: "电白区地质灾害点1", risk: "低风险", type: "地面塌陷"},
    {lng: 110.8455, lat: 21.5887, name: "高州市地质灾害点1", risk: "高风险", type: "滑坡"},
    {lng: 110.7855, lat: 21.5487, name: "化州市地质灾害点1", risk: "中风险", type: "崩塌"},
    {lng: 110.9234, lat: 22.3567, name: "信宜市合水镇地质灾害点1", risk: "高风险", type: "滑坡"},
    {lng: 110.8456, lat: 22.4123, name: "信宜市钱排镇地质灾害点1", risk: "中风险", type: "泥石流"},
    {lng: 110.7890, lat: 22.3890, name: "信宜市新宝镇地质灾害点1", risk: "中风险", type: "崩塌"}
];

// 风险防范区数据（不规则面数据，按风险等级使用不同颜色）
var riskAreas = [
    {
        points: [
            {lng: 110.91, lat: 21.66},
            {lng: 110.94, lat: 21.665},
            {lng: 110.945, lat: 21.69},
            {lng: 110.935, lat: 21.705},
            {lng: 110.92, lat: 21.71},
            {lng: 110.905, lat: 21.695},
            {lng: 110.895, lat: 21.68},
            {lng: 110.905, lat: 21.67}
        ],
        name: "大坡镇高风险防范区",
        risk: "高风险",
        area: "约15.2平方公里",
        population: "约8.5万人",
        towns: ["大坡镇"], // 添加所属镇街信息
        defenseLevel: "一级防范区" // 添加防范区等级
    },
    {
        points: [
            {lng: 110.88, lat: 21.62},
            {lng: 110.92, lat: 21.625},
            {lng: 110.925, lat: 21.645},
            {lng: 110.915, lat: 21.655},
            {lng: 110.89, lat: 21.65},
            {lng: 110.875, lat: 21.635}
        ],
        name: "合水镇风险防范区",
        risk: "中风险",
        area: "约8.7平方公里",
        population: "约4.2万人",
        towns: ["合水镇"], // 添加所属镇街信息
        defenseLevel: "二级防范区" // 添加防范区等级
    },
    {
        points: [
            {lng: 111.02, lat: 21.705},
            {lng: 111.045, lat: 21.71},
            {lng: 111.055, lat: 21.725},
            {lng: 111.05, lat: 21.735},
            {lng: 111.035, lat: 21.73},
            {lng: 111.025, lat: 21.72},
            {lng: 111.015, lat: 21.715}
        ],
        name: "罗坑镇低风险防范区",
        risk: "低风险",
        area: "约6.3平方公里",
        population: "约2.8万人",
        towns: ["罗坑镇"], // 添加所属镇街信息
        defenseLevel: "三级防范区" // 添加防范区等级
    },
    {
        points: [
            {lng: 110.84, lat: 21.585},
            {lng: 110.86, lat: 21.59},
            {lng: 110.865, lat: 21.605},
            {lng: 110.855, lat: 21.615},
            {lng: 110.845, lat: 21.61},
            {lng: 110.835, lat: 21.595}
        ],
        name: "荷花镇高风险防范区",
        risk: "高风险",
        area: "约7.1平方公里",
        population: "约3.6万人",
        towns: ["荷花镇"], // 添加所属镇街信息
        defenseLevel: "一级防范区" // 添加防范区等级
    },
    {
        points: [
            {lng: 110.915, lat: 22.350},
            {lng: 110.930, lat: 22.355},
            {lng: 110.935, lat: 22.365},
            {lng: 110.925, lat: 22.370},
            {lng: 110.910, lat: 22.365},
            {lng: 110.905, lat: 22.355}
        ],
        name: "信宜市合水镇风险防范区",
        risk: "中风险",
        area: "约5.8平方公里",
        population: "约2.1万人",
        towns: ["合水镇"], // 添加所属镇街信息
        defenseLevel: "二级防范区" // 添加防范区等级
    },
    {
        points: [
            {lng: 110.835, lat: 22.405},
            {lng: 110.855, lat: 22.410},
            {lng: 110.860, lat: 22.420},
            {lng: 110.850, lat: 22.425},
            {lng: 110.830, lat: 22.420},
            {lng: 110.825, lat: 22.410}
        ],
        name: "信宜市钱排镇风险防范区",
        risk: "高风险",
        area: "约4.2平方公里",
        population: "约1.8万人",
        towns: ["钱排镇"], // 添加所属镇街信息
        defenseLevel: "一级防范区" // 添加防范区等级
    },
    {
        points: [
            {lng: 110.780, lat: 22.385},
            {lng: 110.800, lat: 22.390},
            {lng: 110.805, lat: 22.400},
            {lng: 110.795, lat: 22.405},
            {lng: 110.775, lat: 22.400},
            {lng: 110.770, lat: 22.390}
        ],
        name: "信宜市新宝镇风险防范区",
        risk: "中风险",
        area: "约3.9平方公里",
        population: "约1.5万人",
        towns: ["新宝镇"], // 添加所属镇街信息
        defenseLevel: "二级防范区" // 添加防范区等级
    },
    {
        points: [
            {lng: 110.815, lat: 22.340},
            {lng: 110.835, lat: 22.345},
            {lng: 110.840, lat: 22.355},
            {lng: 110.830, lat: 22.360},
            {lng: 110.810, lat: 22.355},
            {lng: 110.805, lat: 22.345}
        ],
        name: "信宜市大成镇风险防范区",
        risk: "低风险",
        area: "约4.5平方公里",
        population: "约1.9万人",
        towns: ["大成镇"], // 添加所属镇街信息
        defenseLevel: "三级防范区" // 添加防范区等级
    },
    {
        points: [
            {lng: 110.850, lat: 22.375},
            {lng: 110.870, lat: 22.380},
            {lng: 110.875, lat: 22.390},
            {lng: 110.865, lat: 22.395},
            {lng: 110.845, lat: 22.390},
            {lng: 110.840, lat: 22.380}
        ],
        name: "信宜市朱砂镇风险防范区",
        risk: "中风险",
        area: "约3.7平方公里",
        population: "约1.6万人",
        towns: ["朱砂镇"], // 添加所属镇街信息
        defenseLevel: "二级防范区" // 添加防范区等级
    }
];

// 工具函数：获取风险等级对应的颜色
function getRiskColor(risk) {
    switch(risk) {
        case "高风险": return "#ef4444";
        case "中风险": return "#f59e0b";
        case "低风险": return "#CCC1DA";
        default: return "#6b7280";
    }
}

// 工具函数：获取风险等级对应的防范措施（已废弃，使用getDefenseMeasure替代）
function getRiskMeasure(risk) {
    switch(risk) {
        case "高风险": return "加强监测，及时预警";
        case "中风险": return "定期巡查，预防为主";
        case "低风险": return "按相关规定执行";
        default: return "按相关规定执行";
    }
}

// 根据预警等级和防范区等级获取防御措施
function getDefenseMeasure(warningLevel, defenseLevel) {
    // 防御措施映射表
    var defenseMeasures = {
        1: { // 一级预警（红色）
            "一级防范区": "转移避险；停止户外作业。",
            "二级防范区": "居家避险并做好转移避险准备；停止户外作业；发现异常及时撤离；加强风险巡排查。",
            "三级防范区": "居家避险；停止户外作业；加强风险巡排查。"
        },
        2: { // 二级预警（橙色）
            "一级防范区": "居家避险并做好转移避险准备；发现异常及时撤离；暂停户外作业；加强雨情监测；加强风险巡排查。",
            "二级防范区": "居家避险；发现异常及时撤离；暂停户外作业；加密雨情监测；加强风险巡排查。",
            "三级防范区": "居家避险；暂停户外作业；加密雨情监测；加强风险巡排查。"
        },
        3: { // 三级预警（黄色）
            "一级防范区": "居家避险；发现异常及时撤离；加密雨情监测；加强风险巡排查。",
            "二级防范区": "居家避险；加密雨情监测；加强风险巡排查。",
            "三级防范区": "居家避险；密切关注雨情。"
        },
        4: { // 四级预警（蓝色）
            "一级防范区": "密切关注雨情；加强风险巡排查。",
            "二级防范区": "密切关注雨情。",
            "三级防范区": "密切关注雨情。"
        }
    };

    // 如果没有预警，返回基础防范措施
    if (!warningLevel) {
        return "密切关注天气变化，做好日常防范。";
    }

    // 获取对应的防御措施
    var measures = defenseMeasures[warningLevel];
    if (measures && measures[defenseLevel]) {
        return measures[defenseLevel];
    }

    // 默认返回基础措施
    return "密切关注雨情，加强风险巡排查。";
}
