<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>核心功能特性 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      /* 核心功能页面特定样式 */
      .features-page {
        background: var(--gray-100);
        color: var(--gray-800);
        min-height: 100vh;
      }

      .features-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }

      .features-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image:
          linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%),
          linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%),
          linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.1) 75%),
          linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.1) 75%);
        background-size: 30px 30px;
        background-position: 0 0, 0 15px, 15px -15px, -15px 0px;
        opacity: 0.3;
      }

      .features-content {
        position: relative;
        z-index: 2;
      }

      .features-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }

      .features-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }

      .features-grid {
        display: grid;
        gap: var(--space-6);
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .feature-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        display: flex;
        flex-direction: column;
        min-height: 320px;
        transition: all var(--transition-normal);
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
      }

      .feature-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-light);
      }

      .feature-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-light), var(--primary-color));
        transform: scaleX(0);
        transition: transform var(--transition-normal);
      }

      .feature-card:hover::after {
        transform: scaleX(1);
      }

      .feature-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-5);
      }

      .feature-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 50px;
        text-align: center;
        transition: all var(--transition-normal);
      }

      .feature-card:hover .feature-icon {
        transform: scale(1.2) rotate(5deg);
        color: var(--primary-color);
      }

      .feature-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
      }

      .feature-body {
        flex-grow: 1;
        margin-bottom: var(--space-4);
      }

      .feature-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .feature-list li {
        margin-bottom: var(--space-3);
        position: relative;
        padding-left: var(--space-5);
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }

      .feature-list li:before {
        content: "✓";
        color: var(--primary-light);
        font-weight: 700;
        position: absolute;
        left: 0;
        top: 0;
      }

      .feature-highlight {
        color: var(--primary-light);
        font-weight: 600;
      }

      .feature-visual {
        margin-top: auto;
        text-align: center;
        padding: var(--space-4);
        background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-light) 100%);
        border-radius: var(--radius-md);
        color: var(--white);
        font-weight: 600;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 80px;
        transition: all var(--transition-normal);
      }

      .feature-card:hover .feature-visual {
        background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
        transform: scale(1.05);
      }

      .feature-visual i {
        margin-bottom: var(--space-2);
        font-size: var(--text-3xl);
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 响应式适配 */
      @media (max-width: 479px) {
        .features-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }

        .feature-card {
          min-height: 280px;
          padding: var(--space-4);
        }
      }

      @media (min-width: 480px) and (max-width: 767px) {
        .features-grid {
          grid-template-columns: 1fr;
        }
      }

      @media (min-width: 768px) and (max-width: 1023px) {
        .features-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (min-width: 1024px) {
        .features-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container features-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section features-left">
          <div class="features-content">
            <h1 class="features-title">核心功能特性</h1>
            <p class="features-subtitle">
              茂名市地质灾害预警平台提供四大核心功能模块，满足公众查询、数据管理、预警发布和系统管理的全方位需求。
            </p>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-section">


          <div class="features-grid">
            <!-- 公众查询服务 -->
            <div class="feature-card">
              <div class="feature-header">
                <i class="feature-icon fas fa-search-location"></i>
                <h3 class="feature-title">公众查询服务</h3>
              </div>
              <div class="feature-body">
                <ul class="feature-list">
                  <li><span class="feature-highlight">便民化查询体验</span>：基于天地图服务的地图展示，支持位置定位查询</li>
                  <li><span class="feature-highlight">专业化风险展示</span>：按地质灾害规范颜色显示风险等级</li>
                  <li><span class="feature-highlight">多渠道访问</span>：网站查询和微信公众号查询两种便民渠道</li>
                  <li><span class="feature-highlight">防御措施指引</span>：详细专业的指引地质灾害发生时的有效防御措施</li>
                </ul>
              </div>
              <div class="feature-visual">
                <i class="fas fa-map-marked-alt"></i>
                <div>地图查询服务</div>
              </div>
            </div>

            <!-- 数据管理模块 -->
            <div class="feature-card">
              <div class="feature-header">
                <i class="feature-icon fas fa-database"></i>
                <h3 class="feature-title">数据管理模块</h3>
              </div>
              <div class="feature-body">
                <ul class="feature-list">
                  <li><span class="feature-highlight">地质灾害点管理</span>：支持全市地质灾害点的完整生命周期管理</li>
                  <li><span class="feature-highlight">风险防范区管理</span>：风险防范区基础信息和位置信息统一管理</li>
                  <li><span class="feature-highlight">数据导入导出</span>：支持SHP格式数据导入和批量数据处理</li>
                  <li><span class="feature-highlight">数据备份恢复</span>：确保数据安全可靠的备份恢复机制</li>
                </ul>
              </div>
              <div class="feature-visual">
                <i class="fas fa-server"></i>
                <div>数据管理系统</div>
              </div>
            </div>

            <!-- 预警发布机制 -->
            <div class="feature-card">
              <div class="feature-header">
                <i class="feature-icon fas fa-bell"></i>
                <h3 class="feature-title">预警发布机制</h3>
              </div>
              <div class="feature-body">
                <ul class="feature-list">
                  <li><span class="feature-highlight">多渠道发布能力</span>：微信公众号、网站公告、短信等多种发布渠道</li>
                  <li><span class="feature-highlight">预警信息管理</span>：预警信息编辑发布、等级管理、历史记录</li>
                  <li><span class="feature-highlight">发布流程控制</span>：预警发布双重验证机制，确保发布安全</li>
                </ul>
              </div>
              <div class="feature-visual">
                <i class="fas fa-broadcast-tower"></i>
                <div>预警发布系统</div>
              </div>
            </div>

            <!-- 系统管理模块 -->
            <div class="feature-card">
              <div class="feature-header">
                <i class="feature-icon fas fa-shield-alt"></i>
                <h3 class="feature-title">系统管理模块</h3>
              </div>
              <div class="feature-body">
                <ul class="feature-list">
                  <li><span class="feature-highlight">用户认证与授权</span>：多因子认证机制，确保系统访问安全</li>
                  <li><span class="feature-highlight">权限管理体系</span>：基于角色的精细化权限管理，最小权限原则</li>
                  <li><span class="feature-highlight">操作审计机制</span>：完整记录系统操作，支持责任追溯</li>
                  <li><span class="feature-highlight">安全防护机制</span>：预警发布双重验证，防止误操作和恶意发布</li>
                </ul>
              </div>
              <div class="feature-visual">
                <i class="fas fa-cogs"></i>
                <div>系统管理平台</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="vision_goals.html" class="nav-btn" aria-label="上一页">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">上一页</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="tech_architecture.html" class="nav-btn" aria-label="下一页">
          <span class="mr-2">下一页</span>
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </a>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            window.location.href = 'vision_goals.html';
            break;
          case 'ArrowRight':
            e.preventDefault();
            window.location.href = 'tech_architecture.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });

      // 功能卡片交互增强
      const featureCards = document.querySelectorAll('.feature-card');
      featureCards.forEach((card, index) => {
        // 添加延迟动画
        card.style.animationDelay = `${0.1 * (index + 1)}s`;

        // 鼠标悬停效果
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';
        });

        // 点击效果
        card.addEventListener('click', function() {
          this.style.transform = 'translateY(-4px) scale(0.98)';
          setTimeout(() => {
            this.style.transform = 'translateY(-8px) scale(1.02)';
          }, 150);
        });
      });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>

