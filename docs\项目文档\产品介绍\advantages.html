<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品优势</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'SourceHanSansCN';
        src: url('https://cdn.jsdelivr.net/npm/source-han-sans-cn@1.001/SubsetOTF/CN/SourceHanSansCN-Bold.otf') format('opentype');
        font-weight: bold;
      }
      @font-face {
        font-family: 'MicrosoftYaHei';
        src: url('https://cdn.jsdelivr.net/npm/microsoft-yahei@1.0.0/Microsoft-YaHei.ttf') format('truetype');
      }
      .slide-container {
        width: 100%;
        max-width: 1280px;
        min-height: 720px;
        margin: 0 auto;
        background: #F4F6F7;
        color: #1A5276;
        font-family: 'MicrosoftYaHei', sans-serif;
        display: flex;
      }
      .left-section {
        width: 30%;
        background: #1A5276;
        color: #FFFFFF;
        padding: 2rem 1.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .right-section {
        width: 70%;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .title {
        font-family: 'SourceHanSansCN', sans-serif;
        font-size: clamp(1.5rem, 4vw, 2.25rem);
        font-weight: bold;
        margin-bottom: 1rem;
      }
      .subtitle {
        font-size: clamp(1rem, 2vw, 1.25rem);
        line-height: 1.5;
      }
      .advantages-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-bottom: 1.5rem;
      }
      .advantage-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        min-height: 140px;
      }
      .advantage-title {
        font-size: 1rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .advantage-title i {
        margin-right: 0.5rem;
        font-size: 1.25rem;
      }
      .advantage-content {
        font-size: 0.875rem;
        line-height: 1.4;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .chart-section {
        margin-top: 1rem;
      }
      .chart-title {
        font-size: 1.125rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .chart-title i {
        margin-right: 0.5rem;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
        .advantages-container {
          grid-template-columns: 1fr;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">产品优势</div>
        <div class="subtitle">
          茂名市地质灾害预警平台凭借专业化定位、轻量化设计、本地化服务和完善的安全机制，为用户提供高效、便捷、安全的地质灾害防治信息服务。
        </div>
      </div>
      <div class="right-section">
        <div class="advantages-container">
          <div class="advantage-item">
            <div class="advantage-title">
              <i class="fas fa-award"></i>专业化优势
            </div>
            <div class="advantage-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">垂直领域专精</span>：专注地质灾害防治垂直领域</li>
                <li><span class="highlight">专业化展示</span>：按地质灾害规范标准进行系统设计</li>
                <li><span class="highlight">本地化服务</span>：深度结合茂名市实际情况</li>
              </ul>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-title">
              <i class="fas fa-laptop-code"></i>技术优势
            </div>
            <div class="advantage-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">成熟技术栈</span>：Python FastAPI + Vue3等现代化技术</li>
                <li><span class="highlight">轻量化设计</span>：系统架构简洁清晰，易于维护</li>
                <li><span class="highlight">高可用性</span>：系统可用性达到99.5%以上</li>
              </ul>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-title">
              <i class="fas fa-users"></i>用户体验优势
            </div>
            <div class="advantage-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">便民化设计</span>：网站和微信公众号双渠道查询</li>
                <li><span class="highlight">操作简便</span>：普通用户无需培训即可使用</li>
                <li><span class="highlight">响应迅速</span>：查询响应时间小于3秒</li>
              </ul>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-title">
              <i class="fas fa-shield-alt"></i>安全优势
            </div>
            <div class="advantage-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">多层安全防护</span>：网络层、应用层、数据层全面防护</li>
                <li><span class="highlight">预警双重验证</span>：防止误操作和恶意发布</li>
                <li><span class="highlight">合规性保障</span>：符合网络安全法等相关法规要求</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div class="chart-section">
          <div class="chart-title">
            <i class="fas fa-chart-bar"></i> 与传统系统对比优势
          </div>
          <div style="height: 200px;">
            <canvas id="advantageChart"></canvas>
          </div>
        </div>
      </div>
      
      <div class="navigation">
        <a href="tech_architecture.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="roadmap.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>
    
    <script>
      // 优势对比图表
      const ctx = document.getElementById('advantageChart').getContext('2d');
      const advantageChart = new Chart(ctx, {
        type: 'radar',
        data: {
          labels: ['专业化程度', '本地化适配', '用户体验', '开发成本', '运维成本', '安全性能'],
          datasets: [{
            label: '茂名市地质灾害预警平台',
            data: [90, 95, 85, 75, 80, 90],
            backgroundColor: 'rgba(46, 134, 193, 0.2)',
            borderColor: '#2E86C1',
            borderWidth: 2,
            pointBackgroundColor: '#1A5276'
          }, {
            label: '传统通用系统',
            data: [70, 50, 60, 40, 50, 75],
            backgroundColor: 'rgba(174, 214, 241, 0.2)',
            borderColor: '#AED6F1',
            borderWidth: 2,
            pointBackgroundColor: '#2E86C1'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              angleLines: {
                display: true
              },
              suggestedMin: 0,
              suggestedMax: 100,
              ticks: {
                font: {
                  size: 10
                }
              },
              pointLabels: {
                font: {
                  size: 10
                }
              }
            }
          },
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                font: {
                  size: 10
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

