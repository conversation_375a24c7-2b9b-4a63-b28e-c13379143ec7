<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品优势 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      /* 产品优势页面特定样式 */
      .advantages-page {
        background: var(--gray-100);
        color: var(--gray-800);
        min-height: 100vh;
      }

      .advantages-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }

      .advantages-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image:
          radial-gradient(circle at 25% 25%, rgba(255,255,255,0.15) 2px, transparent 2px),
          radial-gradient(circle at 75% 75%, rgba(255,255,255,0.15) 2px, transparent 2px);
        background-size: 50px 50px;
        opacity: 0.5;
      }

      .advantages-content {
        position: relative;
        z-index: 2;
      }

      .advantages-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }

      .advantages-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }

      .advantages-grid {
        display: grid;
        gap: var(--space-6);
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .advantage-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
      }

      .advantage-card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-light);
      }

      .advantage-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-light);
        transform: scaleY(0);
        transition: transform var(--transition-normal);
      }

      .advantage-card:hover::before {
        transform: scaleY(1);
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-4);
      }

      .card-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 40px;
        text-align: center;
        transition: transform var(--transition-normal);
      }

      .advantage-card:hover .card-icon {
        transform: scale(1.1);
      }

      .card-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
      }

      .card-body {
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }

      .card-body ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .card-body li {
        margin-bottom: var(--space-3);
        position: relative;
        padding-left: var(--space-5);
      }

      .card-body li:before {
        content: "✓";
        color: var(--primary-light);
        font-weight: 700;
        position: absolute;
        left: 0;
        top: 0;
      }

      .highlight-text {
        color: var(--primary-light);
        font-weight: 600;
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 响应式适配 */
      @media (max-width: 479px) {
        .advantages-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }

        .advantage-card {
          padding: var(--space-4);
        }
      }

      @media (min-width: 480px) and (max-width: 767px) {
        .advantages-grid {
          grid-template-columns: 1fr;
        }
      }

      @media (min-width: 768px) and (max-width: 1023px) {
        .advantages-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (min-width: 1024px) {
        .advantages-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container advantages-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section advantages-left">
          <div class="advantages-content">
            <h1 class="advantages-title">产品优势</h1>
            <p class="advantages-subtitle">
              专业化定位、轻量化设计、本地化服务和完善的安全机制，为用户提供高效、便捷、安全的地质灾害防治信息服务。
            </p>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-section">
          <div class="advantages-grid">
            <!-- 专业化优势 -->
            <div class="advantage-card">
              <div class="card-header">
                <i class="card-icon fas fa-graduation-cap"></i>
                <h3 class="card-title">专业化优势</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">垂直领域专注</span>：专注地质灾害防治领域，避免功能冗余</li>
                  <li><span class="highlight-text">专业团队支撑</span>：茂名市自然资源勘探测绘院技术团队</li>
                  <li><span class="highlight-text">本地化深度定制</span>：深度结合茂名市实际需求和特点</li>
                  <li><span class="highlight-text">行业标准遵循</span>：严格按照地质灾害防治相关标准规范</li>
                </ul>
              </div>
            </div>

            <!-- 技术优势 -->
            <div class="advantage-card">
              <div class="card-header">
                <i class="card-icon fas fa-laptop-code"></i>
                <h3 class="card-title">技术优势</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">成熟技术栈</span>：Python FastAPI + Vue3等现代化技术</li>
                  <li><span class="highlight-text">轻量化设计</span>：系统架构简洁清晰，易于维护</li>
                  <li><span class="highlight-text">高可用性</span>：系统可用性达到99.5%以上</li>
                </ul>
              </div>
            </div>

            <!-- 用户体验优势 -->
            <div class="advantage-card">
              <div class="card-header">
                <i class="card-icon fas fa-users"></i>
                <h3 class="card-title">用户体验优势</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">便民化设计</span>：网站和微信公众号双渠道查询</li>
                  <li><span class="highlight-text">操作简便</span>：普通用户无需培训即可使用</li>
                  <li><span class="highlight-text">响应迅速</span>：查询响应时间小于3秒</li>
                </ul>
              </div>
            </div>

            <!-- 安全优势 -->
            <div class="advantage-card">
              <div class="card-header">
                <i class="card-icon fas fa-shield-alt"></i>
                <h3 class="card-title">安全优势</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">多层安全防护</span>：网络层、应用层、数据层全面防护</li>
                  <li><span class="highlight-text">预警双重验证</span>：防止误操作和恶意发布</li>
                  <li><span class="highlight-text">合规性保障</span>：符合网络安全法等相关法规要求</li>
                </ul>
              </div>
            </div>
          </div>


        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="tech_architecture.html" class="nav-btn" aria-label="上一页">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">上一页</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="roadmap.html" class="nav-btn" aria-label="下一页">
          <span class="mr-2">下一页</span>
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </a>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            window.location.href = 'tech_architecture.html';
            break;
          case 'ArrowRight':
            e.preventDefault();
            window.location.href = 'roadmap.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
