/* 茂名市地质灾害预警平台 - 响应式基础样式
 * 创建日期: 2025-07-19
 * 版本: 1.0
 * 描述: 统一的响应式设计基础样式系统
 */

/* ===== CSS变量定义 ===== */
:root {
  /* 颜色系统 */
  --primary-color: #1A5276;
  --primary-light: #2E86C1;
  --primary-dark: #154360;
  --secondary-color: #AED6F1;
  --accent-color: #F39C12;
  --success-color: #27AE60;
  --warning-color: #F39C12;
  --error-color: #E74C3C;
  
  /* 中性色 */
  --white: #FFFFFF;
  --gray-50: #F8F9FA;
  --gray-100: #F4F6F7;
  --gray-200: #E5E8E8;
  --gray-300: #D5DBDB;
  --gray-400: #BDC3C7;
  --gray-500: #85929E;
  --gray-600: #5D6D7E;
  --gray-700: #34495E;
  --gray-800: #2C3E50;
  --gray-900: #1B2631;
  
  /* 字体系统 */
  --font-family-primary: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  --font-family-heading: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  
  /* 字体大小 - 移动优先 */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  
  /* 间距系统 */
  --space-1: 0.25rem;    /* 4px */
  --space-2: 0.5rem;     /* 8px */
  --space-3: 0.75rem;    /* 12px */
  --space-4: 1rem;       /* 16px */
  --space-5: 1.25rem;    /* 20px */
  --space-6: 1.5rem;     /* 24px */
  --space-8: 2rem;       /* 32px */
  --space-10: 2.5rem;    /* 40px */
  --space-12: 3rem;      /* 48px */
  --space-16: 4rem;      /* 64px */
  
  /* 圆角系统 */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.5rem;   /* 8px */
  --radius-lg: 0.75rem;  /* 12px */
  --radius-xl: 1rem;     /* 16px */
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* 断点定义 */
  --breakpoint-xs: 320px;
  --breakpoint-sm: 480px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1440px;
}

/* ===== 基础重置样式 ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== 响应式容器系统 ===== */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--space-4);
}

/* ===== 响应式网格系统 ===== */
.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

/* ===== 响应式字体系统 ===== */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }
.text-5xl { font-size: var(--text-5xl); }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* ===== 响应式间距系统 ===== */
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

/* ===== 颜色系统 ===== */
.text-primary { color: var(--primary-color); }
.text-primary-light { color: var(--primary-light); }
.text-white { color: var(--white); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }

.bg-primary { background-color: var(--primary-color); }
.bg-primary-light { background-color: var(--primary-light); }
.bg-white { background-color: var(--white); }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }

/* ===== 基础组件样式 ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  min-height: 44px; /* 触摸友好的最小高度 */
}

.btn-primary {
  background-color: var(--primary-light);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

/* ===== 响应式断点 ===== */
/* 超小屏幕 (320px - 479px) */
@media (max-width: 479px) {
  .container {
    padding: 0 var(--space-3);
  }
  
  :root {
    --text-3xl: 1.5rem;   /* 24px */
    --text-4xl: 1.875rem; /* 30px */
    --text-5xl: 2.25rem;  /* 36px */
  }
  
  .grid-cols-xs-1 { grid-template-columns: repeat(1, 1fr); }
}

/* 小屏幕 (480px - 767px) */
@media (min-width: 480px) and (max-width: 767px) {
  .grid-cols-sm-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-cols-sm-2 { grid-template-columns: repeat(2, 1fr); }
}

/* 中屏幕 (768px - 1023px) */
@media (min-width: 768px) {
  .container {
    max-width: 768px;
    padding: 0 var(--space-6);
  }
  
  .grid-cols-md-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-cols-md-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-md-3 { grid-template-columns: repeat(3, 1fr); }
}

/* 大屏幕 (1024px - 1439px) */
@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding: 0 var(--space-8);
  }
  
  .grid-cols-lg-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-cols-lg-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-lg-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-lg-4 { grid-template-columns: repeat(4, 1fr); }
}

/* 超大屏幕 (1440px+) */
@media (min-width: 1440px) {
  .container {
    max-width: 1280px;
  }
  
  .grid-cols-xl-1 { grid-template-columns: repeat(1, 1fr); }
  .grid-cols-xl-2 { grid-template-columns: repeat(2, 1fr); }
  .grid-cols-xl-3 { grid-template-columns: repeat(3, 1fr); }
  .grid-cols-xl-4 { grid-template-columns: repeat(4, 1fr); }
  .grid-cols-xl-5 { grid-template-columns: repeat(5, 1fr); }
}
