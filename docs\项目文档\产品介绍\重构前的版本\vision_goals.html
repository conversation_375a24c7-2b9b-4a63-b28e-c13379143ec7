<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品愿景与目标</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'SourceHanSansCN';
        src: url('https://cdn.jsdelivr.net/npm/source-han-sans-cn@1.001/SubsetOTF/CN/SourceHanSansCN-Bold.otf') format('opentype');
        font-weight: bold;
      }
      @font-face {
        font-family: 'MicrosoftYaHei';
        src: url('https://cdn.jsdelivr.net/npm/microsoft-yahei@1.0.0/Microsoft-YaHei.ttf') format('truetype');
      }
      .slide-container {
        width: 100%;
        max-width: 1280px;
        min-height: 720px;
        margin: 0 auto;
        background: #F4F6F7;
        color: #1A5276;
        font-family: 'MicrosoftYaHei', sans-serif;
        display: flex;
      }
      .left-section {
        width: 30%;
        background: #1A5276;
        color: #FFFFFF;
        padding: 2rem 1.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .right-section {
        width: 70%;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .title {
        font-family: 'SourceHanSansCN', sans-serif;
        font-size: clamp(1.5rem, 4vw, 2.25rem);
        font-weight: bold;
        margin-bottom: 1rem;
      }
      .subtitle {
        font-size: clamp(1rem, 2vw, 1.25rem);
        line-height: 1.5;
      }
      .vision-box {
        background: rgba(46, 134, 193, 0.1);
        border-left: 4px solid #2E86C1;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-radius: 0 8px 8px 0;
      }
      .vision-text {
        font-size: 1rem;
        font-style: italic;
        color: #1A5276;
        line-height: 1.5;
      }
      .goals-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1rem;
      }
      .goal-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: flex;
        min-height: 80px;
      }
      .goal-icon {
        width: 50px;
        height: 50px;
        background: #2E86C1;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
      }
      .goal-icon i {
        font-size: 1.25rem;
      }
      .goal-content {
        flex-grow: 1;
      }
      .goal-title {
        font-size: 1rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.5rem;
      }
      .goal-description {
        font-size: 0.875rem;
        line-height: 1.4;
      }
      .kpi-section {
        margin-top: 1rem;
      }
      .kpi-title {
        font-size: 1.125rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .kpi-title i {
        margin-right: 0.5rem;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">产品愿景与目标</div>
        <div class="subtitle">
          茂名市地质灾害预警平台以明确的愿景和目标为指引，致力于成为茂名市地质灾害防治的数字化基础设施，实现"人人知风险、处处有预警"。
        </div>
      </div>
      <div class="right-section">
        <div class="vision-box">
          <div class="vision-text">
            <i class="fas fa-quote-left mr-2 text-lg text-gray-400"></i>
            茂名市地质灾害预警平台致力于成为茂名市民身边的地质安全守护者，通过专业的数据管理和便民的查询服务，让地质灾害风险信息触手可及，让安全防护深入人心。
            <i class="fas fa-quote-right ml-2 text-lg text-gray-400"></i>
          </div>
        </div>
        
        <div class="goals-container">
          <div class="goal-item">
            <div class="goal-icon">
              <i class="fas fa-search"></i>
            </div>
            <div class="goal-content">
              <div class="goal-title">目标一：建立便民化公众查询服务</div>
              <div class="goal-description">
                开发基于地图定位的网站查询服务，查询响应时间小于3秒，计划在<span class="highlight">2周内完成开发并上线</span>。
              </div>
            </div>
          </div>
          
          <div class="goal-item">
            <div class="goal-icon">
              <i class="fas fa-database"></i>
            </div>
            <div class="goal-content">
              <div class="goal-title">目标二：构建高效数据管理体系</div>
              <div class="goal-description">
                建立完整的地质灾害预警平台系统，支持<span class="highlight">74215个地质灾害点</span>数据管理，计划在10周内完成。
              </div>
            </div>
          </div>
          
          <div class="goal-item">
            <div class="goal-icon">
              <i class="fas fa-bell"></i>
            </div>
            <div class="goal-content">
              <div class="goal-title">目标三：建立多渠道预警发布机制</div>
              <div class="goal-description">
                通过微信公众号、网站公告等渠道发布预警信息，预警覆盖率达到<span class="highlight">95%以上</span>，计划在13周内完成。
              </div>
            </div>
          </div>
        </div>
        
        <div class="kpi-section">
          <div class="kpi-title">
            <i class="fas fa-chart-line"></i>关键成功指标
          </div>
          <div style="height: 160px;">
            <canvas id="kpiChart"></canvas>
          </div>
        </div>
      </div>
      
      <div class="navigation">
        <a href="market_needs.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="core_features.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>
    
    <script>
      // 关键成功指标图表
      const ctx = document.getElementById('kpiChart').getContext('2d');
      const kpiChart = new Chart(ctx, {
        type: 'radar',
        data: {
          labels: ['查询服务可用性', '用户满意度', '系统可用性', '查询响应时间', '数据准确率', '预警覆盖率'],
          datasets: [{
            label: '目标值',
            data: [99.5, 90, 99.5, 95, 100, 95],
            backgroundColor: 'rgba(46, 134, 193, 0.2)',
            borderColor: '#2E86C1',
            borderWidth: 2,
            pointBackgroundColor: '#1A5276',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: '#1A5276'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              angleLines: {
                display: true
              },
              suggestedMin: 0,
              suggestedMax: 100,
              ticks: {
                font: {
                  size: 10
                }
              },
              pointLabels: {
                font: {
                  size: 10
                }
              }
            }
          },
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                font: {
                  size: 10
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

