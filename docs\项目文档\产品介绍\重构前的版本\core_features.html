<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>核心功能特性</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'SourceHanSansCN';
        src: url('https://cdn.jsdelivr.net/npm/source-han-sans-cn@1.001/SubsetOTF/CN/SourceHanSansCN-Bold.otf') format('opentype');
        font-weight: bold;
      }
      @font-face {
        font-family: 'MicrosoftYaHei';
        src: url('https://cdn.jsdelivr.net/npm/microsoft-yahei@1.0.0/Microsoft-YaHei.ttf') format('truetype');
      }
      .slide-container {
        width: 100%;
        max-width: 1280px;
        min-height: 720px;
        margin: 0 auto;
        background: #F4F6F7;
        color: #1A5276;
        font-family: 'MicrosoftYaHei', sans-serif;
        display: flex;
      }
      .left-section {
        width: 30%;
        background: #1A5276;
        color: #FFFFFF;
        padding: 2rem 1.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .right-section {
        width: 70%;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .title {
        font-family: 'SourceHanSansCN', sans-serif;
        font-size: clamp(1.5rem, 4vw, 2.25rem);
        font-weight: bold;
        margin-bottom: 1rem;
      }
      .subtitle {
        font-size: clamp(1rem, 2vw, 1.25rem);
        line-height: 1.5;
      }
      .feature-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.25rem;
      }
      .feature-box {
        background: white;
        border-radius: 8px;
        padding: 1.25rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        min-height: 280px;
        display: flex;
        flex-direction: column;
      }
      .feature-title {
        font-size: 1.125rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
      }
      .feature-title i {
        margin-right: 0.5rem;
        font-size: 1.5rem;
      }
      .feature-content {
        font-size: 0.875rem;
        line-height: 1.5;
        flex-grow: 1;
      }
      .feature-visual {
        margin-top: 1rem;
        text-align: center;
        padding: 1rem;
        background: linear-gradient(135deg, #AED6F1 0%, #2E86C1 100%);
        border-radius: 8px;
        color: white;
        font-weight: bold;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 80px;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .feature-list {
        list-style-type: none;
        padding-left: 0;
      }
      .feature-list li {
        margin-bottom: 0.5rem;
        position: relative;
        padding-left: 1rem;
      }
      .feature-list li:before {
        content: "•";
        color: #2E86C1;
        font-weight: bold;
        position: absolute;
        left: 0;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
        .feature-container {
          grid-template-columns: 1fr;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">核心功能特性</div>
        <div class="subtitle">
          茂名市地质灾害预警平台提供四大核心功能模块，满足公众查询、数据管理、预警发布和系统管理的全方位需求。
        </div>
      </div>
      <div class="right-section">
        <div class="feature-container">
          <div class="feature-box">
            <div class="feature-title">
              <i class="fas fa-search-location"></i>公众查询服务
            </div>
            <div class="feature-content">
              <ul class="feature-list">
                <li><span class="highlight">便民化查询体验</span>：基于天地图服务的地图展示，支持位置定位查询</li>
                <li><span class="highlight">专业化风险展示</span>：按地质灾害规范颜色显示风险等级</li>
                <li><span class="highlight">多渠道访问</span>：网站查询和微信公众号查询两种便民渠道</li>
                <li><span class="highlight">预警信息服务</span>：历史预警信息列表展示和区域筛选</li>
              </ul>
            </div>
            <div class="feature-visual">
              <i class="fas fa-map-marked-alt text-3xl mb-2"></i>
              <div>地图查询服务</div>
            </div>
          </div>
          
          <div class="feature-box">
            <div class="feature-title">
              <i class="fas fa-database"></i>数据管理模块
            </div>
            <div class="feature-content">
              <ul class="feature-list">
                <li><span class="highlight">地质灾害点管理</span>：支持74215个地质灾害点的完整生命周期管理</li>
                <li><span class="highlight">风险防范区管理</span>：风险防范区基础信息和位置信息统一管理</li>
                <li><span class="highlight">数据导入导出</span>：支持SHP格式数据导入和批量数据处理</li>
                <li><span class="highlight">数据备份恢复</span>：确保数据安全可靠的备份恢复机制</li>
              </ul>
            </div>
            <div class="feature-visual">
              <i class="fas fa-server text-3xl mb-2"></i>
              <div>数据管理系统</div>
            </div>
          </div>
          
          <div class="feature-box">
            <div class="feature-title">
              <i class="fas fa-bell"></i>预警发布机制
            </div>
            <div class="feature-content">
              <ul class="feature-list">
                <li><span class="highlight">多渠道发布能力</span>：微信公众号、网站公告、短信等多种发布渠道</li>
                <li><span class="highlight">预警信息管理</span>：预警信息编辑发布、等级管理、历史记录</li>
                <li><span class="highlight">发布流程控制</span>：预警发布双重验证机制，确保发布安全</li>
                <li><span class="highlight">覆盖率提升</span>：预警覆盖率从有限覆盖提升至95%以上</li>
              </ul>
            </div>
            <div class="feature-visual">
              <i class="fas fa-broadcast-tower text-3xl mb-2"></i>
              <div>预警发布系统</div>
            </div>
          </div>
          
          <div class="feature-box">
            <div class="feature-title">
              <i class="fas fa-shield-alt"></i>系统管理模块
            </div>
            <div class="feature-content">
              <ul class="feature-list">
                <li><span class="highlight">用户认证与授权</span>：多因子认证机制，确保系统访问安全</li>
                <li><span class="highlight">权限管理体系</span>：基于角色的精细化权限管理，最小权限原则</li>
                <li><span class="highlight">操作审计机制</span>：完整记录系统操作，支持责任追溯</li>
                <li><span class="highlight">安全防护机制</span>：预警发布双重验证，防止误操作和恶意发布</li>
              </ul>
            </div>
            <div class="feature-visual">
              <i class="fas fa-cogs text-3xl mb-2"></i>
              <div>系统管理平台</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="navigation">
        <a href="vision_goals.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="tech_architecture.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>
  </body>
</html>

