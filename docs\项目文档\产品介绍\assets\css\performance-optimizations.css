/* 茂名市地质灾害预警平台 - 性能优化样式
 * 创建日期: 2025-07-19
 * 版本: 1.0
 * 描述: 性能优化相关的CSS规则，包括懒加载、预加载、动画优化等
 */

/* ===== 性能优化基础设置 ===== */
/* 启用硬件加速 */
.slide-container,
.card,
.feature-card,
.overview-card,
.market-card,
.tech-card,
.catalog-item {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 优化动画性能 */
@media (prefers-reduced-motion: no-preference) {
  .nav-btn,
  .back-to-top,
  .card,
  .feature-card,
  .overview-card,
  .market-card,
  .tech-card,
  .catalog-item {
    will-change: transform, opacity;
  }
}

/* 减少动画对于偏好减少动画的用户 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== 图片懒加载优化 ===== */
.lazy-image {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-image.loaded {
  opacity: 1;
}

.lazy-image.loading {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* ===== 字体加载优化 ===== */
/* 字体显示策略 */
@font-face {
  font-family: 'Microsoft YaHei Local';
  src: local('Microsoft YaHei'), local('微软雅黑');
  font-display: swap;
}

/* 字体回退策略 */
.font-optimized {
  font-family: 'Microsoft YaHei Local', 'PingFang SC', 'Hiragino Sans GB', 'Helvetica Neue', Arial, sans-serif;
}

/* ===== 内容可见性优化 ===== */
/* 使用content-visibility优化渲染性能 */
.content-section {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* ===== 滚动性能优化 ===== */
/* 优化滚动性能 */
.scroll-container {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 滚动捕捉 */
.scroll-snap-container {
  scroll-snap-type: y mandatory;
}

.scroll-snap-item {
  scroll-snap-align: start;
}

/* ===== 布局稳定性优化 ===== */
/* 防止布局偏移 */
.aspect-ratio-16-9 {
  aspect-ratio: 16 / 9;
}

.aspect-ratio-4-3 {
  aspect-ratio: 4 / 3;
}

.aspect-ratio-1-1 {
  aspect-ratio: 1 / 1;
}

/* 占位符样式 */
.placeholder {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

/* ===== 交互性能优化 ===== */
/* 优化点击响应 */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
}

/* 防止双击缩放 */
.no-zoom {
  touch-action: pan-x pan-y;
}

/* ===== 渲染优化 ===== */
/* 使用contain属性优化渲染 */
.isolated-component {
  contain: layout style paint;
}

/* GPU加速的动画 */
.gpu-accelerated {
  transform: translate3d(0, 0, 0);
  will-change: transform;
}

/* ===== 网络优化相关 ===== */
/* 预加载关键资源 */
.preload-hint::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1"/></svg>');
}

/* ===== 响应式图片优化 ===== */
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  object-position: center;
}

.responsive-image-contain {
  width: 100%;
  height: auto;
  object-fit: contain;
  object-position: center;
}

/* ===== 关键渲染路径优化 ===== */
/* 首屏内容优先级 */
.above-fold {
  contain: layout;
}

.below-fold {
  content-visibility: auto;
  contain-intrinsic-size: 0 300px;
}

/* ===== 动画性能监控 ===== */
/* 开发环境下的性能监控 */
@media (max-width: 0) {
  .perf-monitor::after {
    content: 'Animation running';
    position: fixed;
    top: 0;
    right: 0;
    background: red;
    color: white;
    padding: 4px;
    font-size: 12px;
    z-index: 9999;
  }
}

/* ===== 浏览器兼容性优化 ===== */
/* Safari特定优化 */
@supports (-webkit-touch-callout: none) {
  .safari-fix {
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
  }
}

/* Firefox特定优化 */
@-moz-document url-prefix() {
  .firefox-fix {
    transform: translateZ(0);
  }
}

/* ===== 内存优化 ===== */
/* 清理不必要的变换 */
.cleanup-transform {
  transform: none;
  will-change: auto;
}

/* ===== 加载状态优化 ===== */
.loading-state {
  pointer-events: none;
  user-select: none;
}

.loading-state::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--gray-300);
  border-top-color: var(--primary-light);
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== 错误状态优化 ===== */
.error-state {
  background: var(--gray-100);
  color: var(--gray-600);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  border-radius: var(--radius-md);
}

.error-state::before {
  content: '⚠️';
  margin-right: var(--space-2);
}

/* ===== 打印优化 ===== */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  .slide-container {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .navigation,
  .back-to-top,
  .progress-indicator,
  .breadcrumb,
  .gesture-hint,
  .keyboard-hint {
    display: none !important;
  }
}

/* ===== 高对比度模式支持 ===== */
@media (prefers-contrast: high) {
  .card,
  .feature-card,
  .overview-card,
  .market-card,
  .tech-card,
  .catalog-item {
    border: 2px solid currentColor;
  }
  
  .nav-btn,
  .back-to-top {
    border: 2px solid currentColor;
  }
}
