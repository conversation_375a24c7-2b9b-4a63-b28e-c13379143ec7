<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术架构</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'SourceHanSansCN';
        src: url('https://cdn.jsdelivr.net/npm/source-han-sans-cn@1.001/SubsetOTF/CN/SourceHanSansCN-Bold.otf') format('opentype');
        font-weight: bold;
      }
      @font-face {
        font-family: 'MicrosoftYaHei';
        src: url('https://cdn.jsdelivr.net/npm/microsoft-yahei@1.0.0/Microsoft-YaHei.ttf') format('truetype');
      }
      .slide-container {
        width: 100%;
        max-width: 1280px;
        min-height: 720px;
        margin: 0 auto;
        background: #F4F6F7;
        color: #1A5276;
        font-family: 'MicrosoftYaHei', sans-serif;
        display: flex;
      }
      .left-section {
        width: 30%;
        background: #1A5276;
        color: #FFFFFF;
        padding: 2rem 1.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .right-section {
        width: 70%;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .title {
        font-family: 'SourceHanSansCN', sans-serif;
        font-size: clamp(1.5rem, 4vw, 2.25rem);
        font-weight: bold;
        margin-bottom: 1rem;
      }
      .subtitle {
        font-size: clamp(1rem, 2vw, 1.25rem);
        line-height: 1.5;
      }
      .section-title {
        font-size: 1.125rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .section-title i {
        margin-right: 0.5rem;
        font-size: 1.25rem;
      }
      .architecture-diagram {
        margin-bottom: 1.5rem;
      }
      .tech-stack {
        display: flex;
        gap: 1rem;
      }
      .tech-column {
        flex: 1;
      }
      .tech-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .tech-title {
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }
      .tech-content {
        font-size: 0.75rem;
        line-height: 1.4;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
        .tech-stack {
          flex-direction: column;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">技术架构</div>
        <div class="subtitle">
          茂名市地质灾害预警平台采用现代化的三层架构设计，包括表现层、业务逻辑层和数据存储层，确保系统的可扩展性、可维护性和安全性。
        </div>
      </div>
      <div class="right-section">
        <div class="architecture-diagram">
          <div class="section-title">
            <i class="fas fa-sitemap"></i> 整体架构设计
          </div>
          <div style="height: 200px;">
            <canvas id="architectureChart"></canvas>
          </div>
        </div>
        
        <div class="tech-stack">
          <div class="tech-column">
            <div class="section-title">
              <i class="fas fa-code"></i> 技术栈选择
            </div>
            <div class="tech-item">
              <div class="tech-title">后端技术栈</div>
              <div class="tech-content">
                <ul class="list-disc pl-4 space-y-1">
                  <li><span class="highlight">Python FastAPI</span>：高性能Web框架</li>
                  <li><span class="highlight">MySQL 8.0</span>：业务数据存储</li>
                  <li><span class="highlight">MongoDB 6.0</span>：GEO矢量数据存储</li>
                  <li><span class="highlight">Nginx</span>：Web服务器和反向代理</li>
                </ul>
              </div>
            </div>
            <div class="tech-item">
              <div class="tech-title">前端技术栈</div>
              <div class="tech-content">
                <ul class="list-disc pl-4 space-y-1">
                  <li><span class="highlight">Vue3</span>：现代化的前端框架</li>
                  <li><span class="highlight">Element Plus</span>：基于Vue3的组件库</li>
                  <li><span class="highlight">Vite</span>：快速的前端构建工具</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="tech-column">
            <div class="section-title">
              <i class="fas fa-server"></i> 基础设施配置
            </div>
            <div class="tech-item">
              <div class="tech-title">服务器配置</div>
              <div class="tech-content">
                <ul class="list-disc pl-4 space-y-1">
                  <li>16核64G云服务器，Ubuntu 24.04 LTS</li>
                  <li>系统盘：100GB ESSD PL1</li>
                  <li>数据盘：500GB增强型SSD</li>
                  <li>网络带宽：30Mbps固定带宽</li>
                </ul>
              </div>
            </div>
            <div class="tech-item">
              <div class="tech-title">第三方服务集成</div>
              <div class="tech-content">
                <ul class="list-disc pl-4 space-y-1">
                  <li><span class="highlight">天地图API</span>：地图服务</li>
                  <li><span class="highlight">微信公众号API</span>：消息推送</li>
                  <li><span class="highlight">短信服务API</span>：预警信息发送</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="navigation">
        <a href="core_features.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="advantages.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>
    
    <script>
      // 架构图表
      const ctx = document.getElementById('architectureChart').getContext('2d');
      
      const architectureChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['表现层', '业务逻辑层', '数据存储层'],
          datasets: [{
            label: '架构层级',
            data: [1, 1, 1],
            backgroundColor: [
              '#AED6F1',
              '#2E86C1',
              '#1A5276'
            ],
            borderColor: [
              '#AED6F1',
              '#2E86C1',
              '#1A5276'
            ],
            borderWidth: 1,
            barPercentage: 0.8
          }]
        },
        options: {
          indexAxis: 'y',
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const labels = [
                    'Vue3 + Element Plus + 响应式设计',
                    'Python FastAPI + Nginx + JWT认证',
                    'MySQL(业务数据) + MongoDB(GEO数据)'
                  ];
                  return labels[context.dataIndex];
                }
              }
            }
          },
          scales: {
            x: {
              display: false,
              grid: {
                display: false
              }
            },
            y: {
              grid: {
                display: false
              },
              ticks: {
                font: {
                  size: 12,
                  weight: 'bold'
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

