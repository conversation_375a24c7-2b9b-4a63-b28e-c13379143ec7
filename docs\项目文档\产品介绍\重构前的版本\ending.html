<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>感谢聆听</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'SourceHanSansCN';
        src: url('https://cdn.jsdelivr.net/npm/source-han-sans-cn@1.001/SubsetOTF/CN/SourceHanSansCN-Bold.otf') format('opentype');
        font-weight: bold;
      }
      @font-face {
        font-family: 'MicrosoftYaHei';
        src: url('https://cdn.jsdelivr.net/npm/microsoft-yahei@1.0.0/Microsoft-YaHei.ttf') format('truetype');
      }
      .slide-container {
        width: 100%;
        max-width: 1280px;
        min-height: 720px;
        margin: 0 auto;
        background: linear-gradient(135deg, #1A5276 0%, #2E86C1 100%);
        color: #FFFFFF;
        font-family: 'MicrosoftYaHei', sans-serif;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 2rem;
        position: relative;
      }
      .main-title {
        font-family: 'SourceHanSansCN', sans-serif;
        font-size: clamp(2.5rem, 6vw, 4rem);
        font-weight: bold;
        margin-bottom: 1rem;
        text-align: center;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }
      .subtitle {
        font-size: clamp(1.2rem, 3vw, 1.8rem);
        margin-bottom: 2rem;
        text-align: center;
        opacity: 0.9;
      }
      .contact-section {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        max-width: 600px;
        width: 100%;
      }
      .contact-title {
        font-size: clamp(1.2rem, 2.5vw, 1.5rem);
        font-weight: bold;
        margin-bottom: 1.5rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .contact-title i {
        margin-right: 0.5rem;
        font-size: clamp(1.5rem, 3vw, 2rem);
      }
      .contact-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        text-align: center;
      }
      .contact-item {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .contact-icon {
        font-size: clamp(1.5rem, 3vw, 2rem);
        margin-bottom: 0.5rem;
        color: #AED6F1;
      }
      .contact-label {
        font-size: clamp(0.9rem, 1.8vw, 1rem);
        font-weight: bold;
        margin-bottom: 0.25rem;
      }
      .contact-value {
        font-size: clamp(0.8rem, 1.6vw, 0.9rem);
        opacity: 0.9;
      }
      .thank-you-message {
        font-size: clamp(1rem, 2vw, 1.25rem);
        text-align: center;
        line-height: 1.6;
        margin-bottom: 2rem;
        max-width: 800px;
      }
      .slogan {
        font-size: clamp(1.1rem, 2.2vw, 1.4rem);
        font-style: italic;
        text-align: center;
        opacity: 0.8;
        margin-top: 1rem;
      }
      .background-pattern {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0.05;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        z-index: -1;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
      }
      @media (max-width: 768px) {
        .slide-container {
          padding: 1rem;
        }
        .contact-section {
          padding: 1.5rem;
        }
        .contact-info {
          grid-template-columns: 1fr;
          gap: 1.5rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="background-pattern"></div>
      
      <div class="main-title">感谢聆听</div>
      <div class="subtitle">Thank You for Your Attention</div>
      
      <div class="thank-you-message">
        感谢您对茂名市地质灾害预警平台的关注与支持。我们将继续致力于为茂名市民提供更加专业、便民、高效的地质灾害防治信息服务，共同守护我们美丽的家园。
      </div>
      
      <div class="contact-section">
        <div class="contact-title">
          <i class="fas fa-address-card"></i>联系我们
        </div>
        <div class="contact-info">
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fas fa-building"></i>
            </div>
            <div class="contact-label">开发单位</div>
            <div class="contact-value">茂名市自然资源勘探测绘院</div>
          </div>
          
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="contact-label">地址</div>
            <div class="contact-value">广东省茂名市</div>
          </div>
          
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fas fa-envelope"></i>
            </div>
            <div class="contact-label">邮箱</div>
            <div class="contact-value"><EMAIL></div>
          </div>
          
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fas fa-globe"></i>
            </div>
            <div class="contact-label">网站</div>
            <div class="contact-value">www.maoming-geo-warning.gov.cn</div>
          </div>
        </div>
      </div>
      
      <div class="slogan">
        让地质灾害风险信息触手可及<br>
        让安全防护深入人心
      </div>
      
      <div class="navigation">
        <a href="conclusion.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
      </div>
    </div>
  </body>
</html>

