<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品概述 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      /* 产品概述页面特定样式 */
      .overview-page {
        background: var(--gray-100);
        color: var(--gray-800);
      }

      .overview-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }

      .overview-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image:
          radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 2px, transparent 2px),
          radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 2px, transparent 2px);
        background-size: 40px 40px;
        opacity: 0.5;
      }

      .overview-content {
        position: relative;
        z-index: 2;
      }

      .overview-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }

      .overview-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }

      .cards-grid {
        display: grid;
        gap: var(--space-6);
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .overview-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        display: flex;
        flex-direction: column;
        min-height: 220px;
        transition: all var(--transition-normal);
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
      }

      .overview-card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-light);
      }

      .overview-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-light);
        transform: scaleY(0);
        transition: transform var(--transition-normal);
      }

      .overview-card:hover::before {
        transform: scaleY(1);
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-4);
      }

      .card-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 40px;
        text-align: center;
        transition: transform var(--transition-normal);
      }

      .overview-card:hover .card-icon {
        transform: scale(1.1);
      }

      .card-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
      }

      .card-body {
        font-size: var(--text-sm);
        line-height: 1.6;
        flex-grow: 1;
        color: var(--gray-700);
      }

      .card-body ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .card-body li {
        margin-bottom: var(--space-3);
        position: relative;
        padding-left: var(--space-5);
      }

      .card-body li:before {
        content: "▶";
        color: var(--primary-light);
        font-size: var(--text-xs);
        position: absolute;
        left: 0;
        top: 2px;
      }

      .highlight-text {
        color: var(--primary-light);
        font-weight: 600;
      }

      .stats-highlight {
        background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 响应式适配 */
      @media (max-width: 479px) {
        .cards-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }

        .overview-card {
          min-height: 180px;
          padding: var(--space-4);
        }
      }

      @media (min-width: 480px) and (max-width: 767px) {
        .cards-grid {
          grid-template-columns: 1fr;
        }
      }

      @media (min-width: 768px) and (max-width: 1023px) {
        .cards-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (min-width: 1024px) {
        .cards-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container overview-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section overview-left">
          <div class="overview-content">
            <h1 class="overview-title">产品概述</h1>
            <p class="overview-subtitle">
              茂名市地质灾害预警平台致力于成为茂名市民身边的地质安全守护者，通过专业的数据管理和便民的查询服务，让地质灾害风险信息触手可及。
            </p>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-section">
          <div class="cards-grid">
            <!-- 产品定位卡片 -->
            <div class="overview-card">
              <div class="card-header">
                <i class="card-icon fas fa-bullseye"></i>
                <h3 class="card-title">产品定位</h3>
              </div>
              <div class="card-body">
                茂名市地质灾害预警平台是由<span class="highlight-text">茂名市自然资源勘探测绘院自主研发</span>的公益型地质灾害防治信息系统，专注于为茂名市民提供便民化的地质灾害风险查询服务。
              </div>
            </div>

            <!-- 服务范围卡片 -->
            <div class="overview-card">
              <div class="card-header">
                <i class="card-icon fas fa-map-marked-alt"></i>
                <h3 class="card-title">服务范围</h3>
              </div>
              <div class="card-body">
                平台覆盖茂名市全域，管理<span class="highlight-text">74215个地质灾害点和风险防范区</span>，涉及全市<span class="highlight-text">90个镇街</span>，服务人口约<span class="highlight-text">472万人</span>。
              </div>
            </div>

            <!-- 核心价值卡片 -->
            <div class="overview-card">
              <div class="card-header">
                <i class="card-icon fas fa-gem"></i>
                <h3 class="card-title">核心价值</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">便民服务</span>：为市民提供免费、便捷的地质灾害风险查询服务</li>
                  <li><span class="highlight-text">高效管理</span>：提升政府部门工作效率50%以上</li>
                  <li><span class="highlight-text">及时预警</span>：多渠道预警发布，提升预警覆盖率</li>
                  <li><span class="highlight-text">科学防灾</span>：提升茂名市地质灾害防治科学化水平</li>
                </ul>
              </div>
            </div>

            <!-- 产品特色卡片 -->
            <div class="overview-card">
              <div class="card-header">
                <i class="card-icon fas fa-star"></i>
                <h3 class="card-title">产品特色</h3>
              </div>
              <div class="card-body">
                <ul>
                  <li><span class="highlight-text">专业化定位</span>：专注地质灾害垂直领域，避免功能冗余</li>
                  <li><span class="highlight-text">本地化服务</span>：深度结合茂名市实际需求</li>
                  <li><span class="highlight-text">公益性质</span>：为市民提供免费安全查询服务</li>
                  <li><span class="highlight-text">轻量化设计</span>：确保系统简单易用、稳定可靠</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="catalog.html" class="nav-btn" aria-label="返回目录">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">目录</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="market_needs.html" class="nav-btn" aria-label="下一页">
          <span class="mr-2">下一页</span>
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </a>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
          case 'ArrowRight':
            e.preventDefault();
            window.location.href = 'market_needs.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });

      // 卡片交互增强
      const cards = document.querySelectorAll('.overview-card');
      cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-6px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';
        });
      });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>

