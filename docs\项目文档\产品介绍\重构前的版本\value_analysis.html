<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价值分析</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'SourceHanSansCN';
        src: url('https://cdn.jsdelivr.net/npm/source-han-sans-cn@1.001/SubsetOTF/CN/SourceHanSansCN-Bold.otf') format('opentype');
        font-weight: bold;
      }
      @font-face {
        font-family: 'MicrosoftYaHei';
        src: url('https://cdn.jsdelivr.net/npm/microsoft-yahei@1.0.0/Microsoft-YaHei.ttf') format('truetype');
      }
      .slide-container {
        width: 100%;
        max-width: 1280px;
        min-height: 720px;
        margin: 0 auto;
        background: #F4F6F7;
        color: #1A5276;
        font-family: 'MicrosoftYaHei', sans-serif;
        display: flex;
      }
      .left-section {
        width: 30%;
        background: #1A5276;
        color: #FFFFFF;
        padding: 2rem 1.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .right-section {
        width: 70%;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .title {
        font-family: 'SourceHanSansCN', sans-serif;
        font-size: clamp(1.5rem, 4vw, 2.25rem);
        font-weight: bold;
        margin-bottom: 1rem;
      }
      .subtitle {
        font-size: clamp(1rem, 2vw, 1.25rem);
        line-height: 1.5;
      }
      .chart-section {
        margin-bottom: 1.5rem;
      }
      .chart-title {
        font-size: 1.125rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .chart-title i {
        margin-right: 0.5rem;
      }
      .value-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
      }
      .value-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .value-icon {
        font-size: 2rem;
        color: #2E86C1;
        margin-bottom: 0.5rem;
      }
      .value-title {
        font-size: 0.875rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.5rem;
      }
      .value-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #1A5276;
        margin-bottom: 0.25rem;
      }
      .value-description {
        font-size: 0.75rem;
        color: #666;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
        .value-container {
          grid-template-columns: repeat(2, 1fr);
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .value-container {
          grid-template-columns: 1fr;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">价值分析</div>
        <div class="subtitle">
          茂名市地质灾害预警平台通过提升公众安全防护能力、优化政府服务效率、降低运营成本，为茂名市创造显著的社会价值和经济价值。
        </div>
      </div>
      <div class="right-section">
        <div class="chart-section">
          <div class="chart-title">
            <i class="fas fa-chart-pie"></i> 价值效益分析
          </div>
          <div style="height: 200px;">
            <canvas id="valueChart"></canvas>
          </div>
        </div>
        
        <div class="value-container">
          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="value-title">服务人口覆盖</div>
            <div class="value-number">472万</div>
            <div class="value-description">茂名市全域人口</div>
          </div>
          
          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="value-title">工作效率提升</div>
            <div class="value-number">50%+</div>
            <div class="value-description">数据管理效率</div>
          </div>
          
          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <div class="value-title">预警覆盖率</div>
            <div class="value-number">95%+</div>
            <div class="value-description">从有限覆盖提升</div>
          </div>
          
          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-map-marked-alt"></i>
            </div>
            <div class="value-title">灾害点管理</div>
            <div class="value-number">74,215</div>
            <div class="value-description">地质灾害点数量</div>
          </div>
          
          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-building"></i>
            </div>
            <div class="value-title">覆盖镇街</div>
            <div class="value-number">90个</div>
            <div class="value-description">茂名市全域镇街</div>
          </div>
          
          <div class="value-item">
            <div class="value-icon">
              <i class="fas fa-tachometer-alt"></i>
            </div>
            <div class="value-title">查询响应时间</div>
            <div class="value-number">&lt;3秒</div>
            <div class="value-description">快速查询体验</div>
          </div>
        </div>
      </div>
      
      <div class="navigation">
        <a href="roadmap.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="conclusion.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>
    
    <script>
      // 价值效益分析图表
      const ctx = document.getElementById('valueChart').getContext('2d');
      const valueChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['社会价值', '政府服务价值', '经济价值', '技术价值'],
          datasets: [{
            data: [35, 30, 20, 15],
            backgroundColor: [
              '#2E86C1',
              '#AED6F1',
              '#1A5276',
              '#85C1E9'
            ],
            borderColor: '#F4F6F7',
            borderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: '平台价值构成分析',
              font: {
                size: 12
              }
            },
            legend: {
              position: 'bottom',
              labels: {
                font: {
                  size: 10
                },
                padding: 10
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const labels = [
                    '提升公众安全防护能力，减少灾害损失',
                    '优化政府服务效率，提升管理水平',
                    '降低运营成本，提高资源利用效率',
                    '推动地质灾害防治信息化建设'
                  ];
                  return `${context.label}: ${context.parsed}% - ${labels[context.dataIndex]}`;
                }
              }
            }
          }
        }
      });
    </script>
  </body>
</html>

