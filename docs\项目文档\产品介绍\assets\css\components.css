/* 茂名市地质灾害预警平台 - 组件样式
 * 创建日期: 2025-07-19
 * 版本: 1.0
 * 描述: 可复用的响应式组件样式库
 */

/* ===== 页面布局组件 ===== */
.slide-container {
  width: 100%;
  min-height: 100vh;
  margin: 0 auto;
  font-family: var(--font-family-primary);
  display: flex;
  flex-direction: column;
  position: relative;
}

.slide-container.full-screen {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: var(--white);
  justify-content: center;
  align-items: center;
  padding: var(--space-8);
}

.slide-container.content-layout {
  background: var(--gray-100);
  color: var(--gray-800);
}

.content-wrapper {
  display: flex;
  flex: 1;
  min-height: calc(100vh - 80px);
}

.left-section {
  background: var(--primary-color);
  color: var(--white);
  padding: var(--space-8);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.right-section {
  padding: var(--space-8);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* ===== 标题组件 ===== */
.page-title {
  font-family: var(--font-family-heading);
  font-weight: 700;
  margin-bottom: var(--space-6);
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.page-subtitle {
  margin-bottom: var(--space-8);
  text-align: center;
  line-height: 1.5;
  opacity: 0.9;
}

.section-title {
  font-family: var(--font-family-heading);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--primary-color);
}

/* ===== 卡片组件 ===== */
.card-container {
  display: grid;
  gap: var(--space-6);
}

.feature-card {
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
  display: flex;
  flex-direction: column;
  min-height: 200px;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-light);
}

.card-title {
  font-weight: 600;
  color: var(--primary-light);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  font-size: var(--text-lg);
}

.card-title i {
  margin-right: var(--space-3);
  font-size: var(--text-xl);
  color: var(--primary-light);
}

.card-content {
  font-size: var(--text-sm);
  line-height: 1.6;
  flex-grow: 1;
  color: var(--gray-700);
}

.card-content ul {
  list-style: none;
  padding: 0;
}

.card-content li {
  margin-bottom: var(--space-2);
  position: relative;
  padding-left: var(--space-4);
}

.card-content li:before {
  content: "•";
  color: var(--primary-light);
  font-weight: bold;
  position: absolute;
  left: 0;
}

.highlight {
  color: var(--primary-light);
  font-weight: 600;
}

/* ===== 导航组件 ===== */
.navigation {
  position: fixed;
  bottom: var(--space-8);
  right: var(--space-8);
  display: flex;
  gap: var(--space-4);
  z-index: 1000;
}

.nav-btn {
  background: var(--primary-light);
  color: var(--white);
  border: none;
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  font-size: var(--text-sm);
  font-weight: 500;
  min-height: 44px;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.nav-btn:hover::before {
  left: 100%;
}

.nav-btn:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.nav-btn i {
  font-size: var(--text-base);
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: var(--space-8);
  left: var(--space-8);
  background: var(--primary-light);
  color: var(--white);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  opacity: 0;
  visibility: hidden;
  z-index: 999;
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background: var(--primary-color);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* 进度指示器 */
.progress-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  z-index: 1001;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-light), var(--primary-color));
  width: 0%;
  transition: width 0.3s ease;
}

/* 面包屑导航 */
.breadcrumb {
  position: fixed;
  top: var(--space-4);
  left: var(--space-4);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  z-index: 1000;
  font-size: var(--text-sm);
}

.breadcrumb a {
  color: var(--primary-light);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.breadcrumb a:hover {
  color: var(--primary-color);
}

.breadcrumb .separator {
  margin: 0 var(--space-2);
  color: var(--gray-400);
}

.breadcrumb .current {
  color: var(--gray-700);
  font-weight: 500;
}

/* 触摸手势提示 */
.gesture-hint {
  position: fixed;
  bottom: var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: var(--white);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  z-index: 1000;
}

.gesture-hint.visible {
  opacity: 1;
  visibility: visible;
}

/* 键盘导航提示 */
.keyboard-hint {
  position: fixed;
  top: 50%;
  right: var(--space-4);
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  font-size: var(--text-xs);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  z-index: 1000;
  max-width: 200px;
}

.keyboard-hint.visible {
  opacity: 1;
  visibility: visible;
}

.keyboard-hint h4 {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-sm);
  color: var(--primary-color);
}

.keyboard-hint ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.keyboard-hint li {
  margin-bottom: var(--space-1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.keyboard-hint .key {
  background: var(--gray-200);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 10px;
}

/* ===== 特殊效果组件 ===== */
.background-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  background-size: cover;
  background-position: center;
  z-index: -1;
}

.feature-visual {
  margin-top: var(--space-4);
  text-align: center;
  padding: var(--space-6);
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-light) 100%);
  border-radius: var(--radius-md);
  color: var(--white);
  font-weight: 600;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80px;
}

.feature-visual i {
  margin-bottom: var(--space-2);
}

/* ===== 目录页面特殊样式 ===== */
.catalog-container {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  min-height: 100vh;
  color: var(--white);
}

.catalog-header {
  text-align: center;
  margin-bottom: var(--space-12);
  padding-top: var(--space-8);
}

.catalog-grid {
  display: grid;
  gap: var(--space-6);
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.catalog-card {
  background: var(--white);
  color: var(--gray-800);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  text-decoration: none;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.catalog-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-light);
}

.catalog-footer {
  text-align: center;
  margin-top: var(--space-12);
  padding-bottom: var(--space-8);
  opacity: 0.9;
}

/* ===== 响应式适配 ===== */
/* 超小屏幕适配 */
@media (max-width: 479px) {
  .slide-container.full-screen {
    padding: var(--space-4);
  }

  .page-title {
    font-size: var(--text-3xl);
  }

  .page-subtitle {
    font-size: var(--text-lg);
  }

  .left-section,
  .right-section {
    padding: var(--space-4);
  }

  .card-container {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .navigation {
    bottom: var(--space-4);
    right: var(--space-4);
    flex-direction: column;
    gap: var(--space-2);
  }

  .nav-btn {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-xs);
  }

  .catalog-grid {
    grid-template-columns: 1fr;
    padding: 0 var(--space-3);
  }
}

/* 小屏幕适配 */
@media (min-width: 480px) and (max-width: 767px) {
  .card-container {
    grid-template-columns: 1fr;
  }

  .catalog-grid {
    grid-template-columns: 1fr;
  }
}

/* 中屏幕适配 */
@media (min-width: 768px) and (max-width: 1023px) {
  .content-wrapper {
    flex-direction: column;
  }

  .left-section,
  .right-section {
    width: 100%;
  }

  .left-section {
    min-height: auto;
    padding: var(--space-6);
  }

  .right-section {
    padding: var(--space-6);
  }

  .card-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .catalog-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 大屏幕适配 */
@media (min-width: 1024px) {
  .content-wrapper {
    flex-direction: row;
  }

  .left-section {
    width: 35%;
  }

  .right-section {
    width: 65%;
  }

  .card-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .catalog-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 超大屏幕适配 */
@media (min-width: 1440px) {
  .card-container {
    grid-template-columns: repeat(3, 1fr);
  }

  .catalog-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
