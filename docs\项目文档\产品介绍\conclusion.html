<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>总结与展望</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1"></script>
    <style>
      @font-face {
        font-family: 'SourceHanSansCN';
        src: url('https://cdn.jsdelivr.net/npm/source-han-sans-cn@1.001/SubsetOTF/CN/SourceHanSansCN-Bold.otf') format('opentype');
        font-weight: bold;
      }
      @font-face {
        font-family: 'MicrosoftYaHei';
        src: url('https://cdn.jsdelivr.net/npm/microsoft-yahei@1.0.0/Microsoft-YaHei.ttf') format('truetype');
      }
      .slide-container {
        width: 100%;
        max-width: 1280px;
        min-height: 720px;
        margin: 0 auto;
        background: #F4F6F7;
        color: #1A5276;
        font-family: 'MicrosoftYaHei', sans-serif;
        display: flex;
      }
      .left-section {
        width: 30%;
        background: #1A5276;
        color: #FFFFFF;
        padding: 2rem 1.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .right-section {
        width: 70%;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
      .title {
        font-family: 'SourceHanSansCN', sans-serif;
        font-size: clamp(1.5rem, 4vw, 2.25rem);
        font-weight: bold;
        margin-bottom: 1rem;
      }
      .subtitle {
        font-size: clamp(1rem, 2vw, 1.25rem);
        line-height: 1.5;
      }
      .summary-box {
        background: rgba(46, 134, 193, 0.1);
        border-left: 4px solid #2E86C1;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-radius: 0 8px 8px 0;
      }
      .summary-text {
        font-size: 1rem;
        line-height: 1.5;
        color: #1A5276;
      }
      .future-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
      }
      .future-item {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        min-height: 120px;
      }
      .future-title {
        font-size: 1rem;
        font-weight: bold;
        color: #2E86C1;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
      }
      .future-title i {
        margin-right: 0.5rem;
        font-size: 1.25rem;
      }
      .future-content {
        font-size: 0.875rem;
        line-height: 1.4;
      }
      .highlight {
        color: #2E86C1;
        font-weight: bold;
      }
      .navigation {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        display: flex;
        gap: 1rem;
        z-index: 1000;
      }
      .nav-btn {
        background: #2E86C1;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
      }
      .nav-btn:hover {
        background: #1A5276;
        transform: translateY(-2px);
      }
      @media (max-width: 1024px) {
        .slide-container {
          flex-direction: column;
          min-height: 100vh;
        }
        .left-section, .right-section {
          width: 100%;
        }
        .left-section {
          min-height: auto;
          padding: 1.5rem;
        }
        .right-section {
          padding: 1.5rem;
        }
        .future-container {
          grid-template-columns: 1fr;
        }
      }
      @media (max-width: 768px) {
        .left-section, .right-section {
          padding: 1rem;
        }
        .navigation {
          bottom: 1rem;
          right: 1rem;
          flex-direction: column;
        }
        .nav-btn {
          padding: 0.5rem 1rem;
          font-size: 0.875rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container">
      <div class="left-section">
        <div class="title">总结与展望</div>
        <div class="subtitle">
          茂名市地质灾害预警平台作为茂名市民身边的地质安全守护者，将持续为提升公众安全防护能力、优化政府服务效率贡献力量。
        </div>
      </div>
      <div class="right-section">
        <div class="summary-box">
          <div class="summary-text">
            <strong>核心价值总结：</strong>茂名市地质灾害预警平台通过专业化的地质灾害防治信息服务，为茂名市472万群众提供便民化的风险查询服务，为政府部门提供高效的数据管理和预警发布能力，实现了"让地质灾害风险信息触手可及，让安全防护深入人心"的产品愿景。
          </div>
        </div>
        
        <div class="future-container">
          <div class="future-item">
            <div class="future-title">
              <i class="fas fa-rocket"></i>技术发展方向
            </div>
            <div class="future-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">智能化升级</span>：引入AI技术提升预警准确性</li>
                <li><span class="highlight">移动端优化</span>：开发专用移动应用</li>
                <li><span class="highlight">数据可视化</span>：增强地图展示和数据分析能力</li>
              </ul>
            </div>
          </div>
          
          <div class="future-item">
            <div class="future-title">
              <i class="fas fa-expand-arrows-alt"></i>服务拓展计划
            </div>
            <div class="future-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">区域扩展</span>：向粤西地区其他城市推广</li>
                <li><span class="highlight">功能扩展</span>：增加气象预警、应急响应等功能</li>
                <li><span class="highlight">数据共享</span>：与省级平台实现数据互联互通</li>
              </ul>
            </div>
          </div>
          
          <div class="future-item">
            <div class="future-title">
              <i class="fas fa-users-cog"></i>用户体验优化
            </div>
            <div class="future-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">个性化服务</span>：基于用户位置的精准推送</li>
                <li><span class="highlight">多语言支持</span>：支持粤语等本地方言</li>
                <li><span class="highlight">无障碍访问</span>：提升老年人和残障人士使用体验</li>
              </ul>
            </div>
          </div>
          
          <div class="future-item">
            <div class="future-title">
              <i class="fas fa-handshake"></i>合作发展机遇
            </div>
            <div class="future-content">
              <ul class="list-disc pl-4 space-y-1">
                <li><span class="highlight">政企合作</span>：与电信运营商深化预警发布合作</li>
                <li><span class="highlight">学术合作</span>：与高校科研院所开展技术创新</li>
                <li><span class="highlight">标准制定</span>：参与地质灾害信息化标准制定</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div class="navigation">
        <a href="value_analysis.html" class="nav-btn">
          <i class="fas fa-arrow-left mr-2"></i>上一页
        </a>
        <a href="index.html" class="nav-btn">
          <i class="fas fa-home mr-2"></i>首页
        </a>
        <a href="ending.html" class="nav-btn">
          <i class="fas fa-arrow-right mr-2"></i>下一页
        </a>
      </div>
    </div>
  </body>
</html>

