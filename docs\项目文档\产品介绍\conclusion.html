<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>总结与展望 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      .conclusion-page {
        background: var(--gray-100);
        color: var(--gray-800);
        min-height: 100vh;
      }
      
      .conclusion-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }
      
      .conclusion-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image: 
          radial-gradient(circle at 50% 50%, rgba(255,255,255,0.2) 2px, transparent 2px);
        background-size: 30px 30px;
        opacity: 0.5;
      }
      
      .conclusion-content {
        position: relative;
        z-index: 2;
      }
      
      .conclusion-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }
      
      .conclusion-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }
      
      .conclusion-grid {
        display: grid;
        gap: var(--space-6);
        animation: fadeInUp 1s ease-out 0.4s both;
      }
      
      .conclusion-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
      }
      
      .conclusion-card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-light);
      }
      
      .conclusion-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--primary-light);
        transform: scaleY(0);
        transition: transform var(--transition-normal);
      }
      
      .conclusion-card:hover::before {
        transform: scaleY(1);
      }
      
      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-4);
      }
      
      .card-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 40px;
        text-align: center;
        transition: transform var(--transition-normal);
      }
      
      .conclusion-card:hover .card-icon {
        transform: scale(1.1);
      }
      
      .card-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
      }
      
      .card-body {
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }
      
      .highlight-text {
        color: var(--primary-light);
        font-weight: 600;
      }
      
      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }
      
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      /* 响应式适配 */
      @media (max-width: 479px) {
        .conclusion-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }
        
        .conclusion-card {
          padding: var(--space-4);
        }
      }
      
      @media (min-width: 480px) and (max-width: 767px) {
        .conclusion-grid {
          grid-template-columns: 1fr;
        }
      }
      
      @media (min-width: 768px) and (max-width: 1023px) {
        .conclusion-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
      
      @media (min-width: 1024px) {
        .conclusion-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container conclusion-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section conclusion-left">
          <div class="conclusion-content">
            <h1 class="conclusion-title">总结与展望</h1>
            <p class="conclusion-subtitle">
              茂名市地质灾害预警平台将成为保护人民生命财产安全的重要工具，为建设安全茂名贡献力量。
            </p>
          </div>
        </div>
        
        <!-- 右侧内容区域 -->
        <div class="right-section">
          <div class="conclusion-grid">
            <!-- 核心价值总结 -->
            <div class="conclusion-card">
              <div class="card-header">
                <i class="card-icon fas fa-gem"></i>
                <h3 class="card-title">核心价值总结</h3>
              </div>
              <div class="card-body">
                <p>茂名市地质灾害预警平台以<span class="highlight-text">"人人知风险、处处有预警"</span>为目标，通过数字化手段提升地质灾害防治能力，为472万群众提供安全保障，实现政府服务效率提升50%以上，预警覆盖率达到95%以上。</p>
              </div>
            </div>
            
            <!-- 技术创新亮点 -->
            <div class="conclusion-card">
              <div class="card-header">
                <i class="card-icon fas fa-lightbulb"></i>
                <h3 class="card-title">技术创新亮点</h3>
              </div>
              <div class="card-body">
                <p>平台采用<span class="highlight-text">现代化技术架构</span>，实现轻量化、高性能的系统设计。通过微信公众号集成、多渠道预警发布、响应式界面设计等创新功能，为用户提供便捷、高效的服务体验。</p>
              </div>
            </div>
            
            <!-- 社会意义 -->
            <div class="conclusion-card">
              <div class="card-header">
                <i class="card-icon fas fa-heart"></i>
                <h3 class="card-title">社会意义</h3>
              </div>
              <div class="card-body">
                <p>平台建设具有重要的<span class="highlight-text">社会价值和现实意义</span>，不仅提升了茂名市地质灾害防治的科学化水平，更为保护人民生命财产安全、促进经济社会可持续发展提供了有力支撑。</p>
              </div>
            </div>
            
            <!-- 未来展望 -->
            <div class="conclusion-card">
              <div class="card-header">
                <i class="card-icon fas fa-rocket"></i>
                <h3 class="card-title">未来展望</h3>
              </div>
              <div class="card-body">
                <p>未来将持续<span class="highlight-text">优化平台功能</span>，拓展服务范围，引入人工智能、大数据分析等先进技术，构建更加智慧化、精准化的地质灾害防治体系，为建设安全茂名、美丽茂名贡献更大力量。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="value_analysis.html" class="nav-btn" aria-label="上一页">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">上一页</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="ending.html" class="nav-btn" aria-label="结束页">
          <span class="mr-2">结束</span>
          <i class="fas fa-flag-checkered" aria-hidden="true"></i>
        </a>
      </div>
    </div>
    
    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });
      
      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            window.location.href = 'value_analysis.html';
            break;
          case 'ArrowRight':
            e.preventDefault();
            window.location.href = 'ending.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });
    </script>
    
    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
