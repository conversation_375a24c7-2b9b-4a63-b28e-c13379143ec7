<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价值分析 - 茂名市地质灾害预警平台</title>
    <!-- 本地CSS文件 -->
    <link rel="stylesheet" href="assets/css/responsive-base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <!-- Font Awesome图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
      .value-page {
        background: var(--gray-100);
        color: var(--gray-800);
        min-height: 100vh;
      }

      .value-left {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        color: var(--white);
        position: relative;
        overflow: hidden;
      }

      .value-left::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-image:
          radial-gradient(circle at 20% 80%, rgba(255,255,255,0.15) 2px, transparent 2px),
          radial-gradient(circle at 80% 20%, rgba(255,255,255,0.15) 2px, transparent 2px);
        background-size: 60px 60px;
        opacity: 0.5;
      }

      .value-content {
        position: relative;
        z-index: 2;
      }

      .value-title {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        font-weight: 700;
        margin-bottom: var(--space-6);
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: slideInLeft 1s ease-out;
      }

      .value-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.3rem);
        line-height: 1.6;
        opacity: 0.95;
        animation: slideInLeft 1s ease-out 0.2s both;
      }

      .value-main {
        display: flex;
        flex-direction: column;
        gap: var(--space-8);
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .chart-section {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
      }

      .chart-section:hover {
        border-color: var(--primary-light);
        box-shadow: var(--shadow-xl);
      }

      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-4);
      }

      .section-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 50px;
        text-align: center;
      }

      .section-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
      }

      .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: var(--space-4);
      }

      .value-grid {
        display: grid;
        gap: var(--space-6);
      }

      .value-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--space-6);
        box-shadow: var(--shadow-md);
        border: 2px solid transparent;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
      }

      .value-card:hover {
        transform: translateY(-4px);
        border-color: var(--primary-light);
        box-shadow: var(--shadow-xl);
      }

      .value-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-light), var(--primary-color));
        transform: scaleX(0);
        transition: transform var(--transition-normal);
      }

      .value-card:hover::before {
        transform: scaleX(1);
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--space-4);
      }

      .card-icon {
        font-size: var(--text-2xl);
        color: var(--primary-light);
        margin-right: var(--space-4);
        width: 40px;
        text-align: center;
        transition: transform var(--transition-normal);
      }

      .value-card:hover .card-icon {
        transform: scale(1.1);
      }

      .card-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--gray-800);
      }

      .card-body {
        font-size: var(--text-sm);
        line-height: 1.6;
        color: var(--gray-700);
      }

      .highlight-text {
        color: var(--primary-light);
        font-weight: 600;
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* 响应式适配 */
      @media (max-width: 479px) {
        .value-grid {
          grid-template-columns: 1fr;
          gap: var(--space-4);
        }

        .value-card {
          padding: var(--space-4);
        }

        .chart-container {
          height: 250px;
        }

        .chart-section {
          padding: var(--space-4);
        }
      }

      @media (min-width: 480px) and (max-width: 767px) {
        .value-grid {
          grid-template-columns: 1fr;
        }

        .chart-container {
          height: 280px;
        }
      }

      @media (min-width: 768px) and (max-width: 1023px) {
        .value-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (min-width: 1024px) {
        .value-grid {
          grid-template-columns: repeat(3, 1fr);
        }
      }
    </style>
  </head>
  <body>
    <div class="slide-container value-page content-layout">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="left-section value-left">
          <div class="value-content">
            <h1 class="value-title">价值分析</h1>
            <p class="value-subtitle">
              全面分析平台的社会价值、政府服务价值和经济社会价值，展现项目的重要意义。
            </p>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-section">
          <div class="value-main">
            <!-- 价值分布图表 -->
            <div class="chart-section">
              <div class="section-header">
                <i class="section-icon fas fa-chart-pie"></i>
                <h3 class="section-title">价值分布分析</h3>
              </div>


            </div>

            <!-- 价值详细分析 -->
            <div class="value-grid">
              <!-- 社会价值 -->
              <div class="value-card">
                <div class="card-header">
                  <i class="card-icon fas fa-users"></i>
                  <h3 class="card-title">社会价值</h3>
                </div>
                <div class="card-body">
                  <p><span class="highlight-text">保护人民生命财产安全</span>：提升472万群众的地质灾害防护能力，减少人员伤亡和财产损失。</p>
                  <br>
                  <p><span class="highlight-text">提升公众安全意识</span>：通过便民查询服务，增强公众对地质灾害风险的认知和防范意识。</p>
                </div>
              </div>

              <!-- 政府服务价值 -->
              <div class="value-card">
                <div class="card-header">
                  <i class="card-icon fas fa-building"></i>
                  <h3 class="card-title">政府服务价值</h3>
                </div>
                <div class="card-body">
                  <p><span class="highlight-text">提升工作效率</span>：数字化管理提升政府部门工作效率50%以上，减少人工成本。</p>
                  <br>
                  <p><span class="highlight-text">优化公共服务</span>：为市民提供24小时在线查询服务，提升政府服务水平。</p>
                </div>
              </div>

              <!-- 经济社会价值 -->
              <div class="value-card">
                <div class="card-header">
                  <i class="card-icon fas fa-chart-line"></i>
                  <h3 class="card-title">经济社会价值</h3>
                </div>
                <div class="card-body">
                  <p><span class="highlight-text">降低灾害损失</span>：有效预警可减少地质灾害造成的经济损失，保护社会财富。</p>
                  <br>
                  <p><span class="highlight-text">促进可持续发展</span>：科学防灾减灾，促进茂名市经济社会可持续发展。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="navigation">
        <a href="roadmap.html" class="nav-btn" aria-label="上一页">
          <i class="fas fa-arrow-left" aria-hidden="true"></i>
          <span class="ml-2">上一页</span>
        </a>
        <a href="index.html" class="nav-btn" aria-label="返回首页">
          <i class="fas fa-home" aria-hidden="true"></i>
          <span class="ml-2">首页</span>
        </a>
        <a href="conclusion.html" class="nav-btn" aria-label="下一页">
          <span class="mr-2">下一页</span>
          <i class="fas fa-arrow-right" aria-hidden="true"></i>
        </a>
      </div>
    </div>

    <!-- 交互脚本 -->
    <script>
      // 页面加载动画
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.opacity = '1';
      });

      // 键盘导航支持
      document.addEventListener('keydown', function(e) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            window.location.href = 'roadmap.html';
            break;
          case 'ArrowRight':
            e.preventDefault();
            window.location.href = 'conclusion.html';
            break;
          case 'Home':
            e.preventDefault();
            window.location.href = 'index.html';
            break;
          case 'Escape':
            e.preventDefault();
            window.location.href = 'catalog.html';
            break;
        }
      });
    </script>

    <style>
      /* 页面加载动画 */
      body {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }
    </style>
  </body>
</html>
